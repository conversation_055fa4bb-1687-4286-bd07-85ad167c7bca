/**
 * 歌曲解锁失败诊断脚本
 * 用于深入分析"This song \"?\" is not available in any source"错误
 */

const axios = require('axios');

async function diagnoseSongUnlock() {
    console.log('🔍 开始诊断歌曲解锁失败问题...\n');

    const baseUrl = 'http://localhost:50090';

    // 测试歌曲信息
    const testCases = [
        { id: 418602084, name: '说走就走', sources: ['qq', 'migu', 'kuwo'] },
        { id: 186016, name: '青花瓷', sources: ['qq', 'migu', 'kuwo'] },
        { id: 123456, name: '测试无效ID', sources: ['qq', 'migu'] }
    ];

    for (const testCase of testCases) {
        console.log(`📀 测试歌曲: ${testCase.name} (ID: ${testCase.id})`);
        console.log('=' .repeat(60));

        for (const source of testCase.sources) {
            try {
                console.log(`🎵 尝试音源: ${source}`);

                const response = await axios.get(`${baseUrl}/music/unlock`, {
                    params: {
                        sources: source,
                        songs: testCase.id
                    },
                    timeout: 15000
                });

                const data = response.data;

                if (data.解锁成功 > 0) {
                    console.log(`  ✅ 成功: ${source}`);
                    console.log(`     - 音源: ${data.成功列表.音源名称}`);
                    console.log(`     - 音质: ${data.成功列表.音质} (${data.成功列表.音质描述})`);
                    console.log(`     - 文件大小: ${data.成功列表.文件大小} bytes`);
                    console.log(`     - 格式: ${data.成功列表.格式}`);
                } else {
                    console.log(`  ❌ 失败: ${source}`);
                    console.log(`     - 失败原因: ${data.失败列表.失败原因}`);

                    // 分析失败原因
                    if (data.失败列表.失败原因.includes('not available in any source')) {
                        console.log(`     - 分析: 该音源不支持此歌曲，可能原因:`);
                        console.log(`       * 歌曲在该平台不存在`);
                        console.log(`       * 需要VIP权限或特殊认证`);
                        console.log(`       * 地区限制`);
                        console.log(`       * 版权问题`);
                    }
                }

            } catch (error) {
                console.log(`  ❌ 网络错误: ${source}`);
                console.log(`     - 错误: ${error.message}`);
            }
        }

        console.log('\n');
    }

    // 测试多音源组合
    console.log('🔧 测试多音源组合解锁...');
    console.log('=' .repeat(60));

    const multiSourceTests = [
        { sources: 'qq,migu', name: 'QQ+咪咕' },
        { sources: 'migu,kuwo', name: '咪咕+酷我' },
        { sources: 'qq,migu,kuwo,kugou', name: '全部音源' }
    ];

    for (const test of multiSourceTests) {
        try {
            console.log(`🎵 测试组合: ${test.name} (${test.sources})`);

            const response = await axios.get(`${baseUrl}/music/unlock`, {
                params: {
                    sources: test.sources,
                    songs: '418602084'
                },
                timeout: 20000
            });

            const data = response.data;
            console.log(`  📊 结果: 成功${data.解锁成功}/${data.解锁总数}, 成功率${data.解锁成功率}`);
            console.log(`  🎯 配置来源: ${data.音源配置来源}`);
            console.log(`  🎼 使用音源: ${data.使用的音源.join(', ')}`);

            if (data.解锁成功 > 0) {
                console.log(`  ✅ 成功音源: ${data.成功列表.音源名称} (${data.成功列表.音质描述})`);
            }

        } catch (error) {
            console.log(`  ❌ 测试失败: ${error.message}`);
        }
    }

    console.log('\n🎯 诊断完成！');
    console.log('\n📋 建议解决方案:');
    console.log('1. QQ音乐可能需要Cookie认证或VIP权限');
    console.log('2. 建议优先使用咪咕音乐，成功率较高');
    console.log('3. 可以尝试多音源组合提高成功率');
    console.log('4. 检查网络连接和防火墙设置');
}

// 运行诊断
diagnoseSongUnlock().catch(console.error);
