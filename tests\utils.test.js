/**
 * Utils模块单元测试
 * 测试response.js和constants.js的功能
 */

// response工具已删除，功能已简化到路由层

const {
    HTTP_STATUS,
    ERROR_CODES,
    MUSIC_SOURCES,
    SOURCE_DISPLAY_NAMES,
    QUALITY_LEVELS,
    API_LIMITS
} = require('../src/utils/constants');

// Response Utils测试已删除，因为response工具已被移除

describe('Constants测试', () => {
    test('HTTP_STATUS常量', () => {
        expect(HTTP_STATUS.OK).toBe(200);
        expect(HTTP_STATUS.BAD_REQUEST).toBe(400);
        expect(HTTP_STATUS.NOT_FOUND).toBe(404);
        expect(HTTP_STATUS.INTERNAL_SERVER_ERROR).toBe(500);
        expect(HTTP_STATUS.SERVICE_UNAVAILABLE).toBe(503);
    });

    test('ERROR_CODES常量', () => {
        expect(ERROR_CODES.VALIDATION_ERROR).toBe('VALIDATION_ERROR');
        expect(ERROR_CODES.SONG_NOT_FOUND).toBe('SONG_NOT_FOUND');
        expect(ERROR_CODES.INTERNAL_ERROR).toBe('INTERNAL_ERROR');
        expect(ERROR_CODES.SOURCE_UNAVAILABLE).toBe('SOURCE_UNAVAILABLE');
    });



    test('MUSIC_SOURCES常量', () => {
        expect(typeof MUSIC_SOURCES).toBe('object');
        expect(MUSIC_SOURCES.QQ).toBe('qq');
        expect(MUSIC_SOURCES.KUGOU).toBe('kugou');
        expect(MUSIC_SOURCES.KUWO).toBe('kuwo');
        expect(MUSIC_SOURCES.MIGU).toBe('migu');
    });

    test('SOURCE_DISPLAY_NAMES常量', () => {
        expect(typeof SOURCE_DISPLAY_NAMES).toBe('object');
        expect(SOURCE_DISPLAY_NAMES.qq).toBe('QQ音乐');
        expect(SOURCE_DISPLAY_NAMES.kugou).toBe('酷狗音乐');
    });

    test('QUALITY_LEVELS常量', () => {
        expect(typeof QUALITY_LEVELS).toBe('object');
        expect(QUALITY_LEVELS.LOW).toBe(128000);
        expect(QUALITY_LEVELS.STANDARD).toBe(192000);
        expect(QUALITY_LEVELS.HIGH).toBe(320000);
        expect(QUALITY_LEVELS.LOSSLESS).toBe(999000);
    });

    test('API_LIMITS常量', () => {
        expect(typeof API_LIMITS).toBe('object');
        expect(typeof API_LIMITS.MAX_BATCH_SIZE).toBe('number');
        expect(typeof API_LIMITS.REQUEST_TIMEOUT).toBe('number');
        expect(API_LIMITS.MAX_BATCH_SIZE).toBeGreaterThan(0);
        expect(API_LIMITS.REQUEST_TIMEOUT).toBeGreaterThan(0);
    });
});
