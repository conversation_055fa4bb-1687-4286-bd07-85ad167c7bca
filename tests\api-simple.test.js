/**
 * 简化的API测试脚本
 * 使用Jest进行核心API接口测试
 */

const request = require('supertest');
const app = require('../src/app');

// 设置测试超时时间
jest.setTimeout(30000);

// 测试配置
const TEST_SONG_ID = '418602084'; // 周杰伦-稻香
const TEST_SEARCH_KEYWORD = '周杰伦';
const API_BASE = '/api';

describe('音乐解锁服务API测试', () => {
    let server;

    beforeAll(async () => {
        // 使用不同的端口进行测试
        process.env.PORT = '50091';
        process.env.NODE_ENV = 'test';
    });

    afterAll(async () => {
        if (server) {
            server.close();
        }
    });

    // 健康检查测试
    describe('健康检查', () => {
        test('GET /health - 应该返回服务健康状态', async () => {
            const response = await request(app)
                .get('/health')
                .expect(200);

            expect(response.body.code).toBe(200);
            expect(response.body.data.status).toBe('healthy');
        });
    });

    // API信息测试
    describe('API信息', () => {
        test('GET /api - 应该返回API基本信息', async () => {
            const response = await request(app)
                .get(API_BASE)
                .expect(200);
            
            expect(response.body.name).toBe('音乐解锁服务API');
            expect(response.body.version).toBeDefined();
        });

        test('GET /api/docs - 应该返回API文档', async () => {
            const response = await request(app)
                .get(`${API_BASE}/docs`)
                .expect(200);
            
            expect(response.body.title).toBe('音乐解锁服务API文档');
            expect(response.body.endpoints).toBeDefined();
        });
    });

    // 歌曲信息API测试
    describe('歌曲信息API', () => {
        test('GET /api/song/:id - 应该返回歌曲信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/song/${TEST_SONG_ID}`)
                .expect(200);
            
            expect(response.body.code).toBe(200);
            expect(response.body.data.id).toBe(parseInt(TEST_SONG_ID));
        });

        test('GET /api/metadata/:id - 应该返回歌曲元数据', async () => {
            const response = await request(app)
                .get(`${API_BASE}/metadata/${TEST_SONG_ID}`)
                .expect(200);
            
            expect(response.body.code).toBe(200);
            expect(response.body.data.id).toBe(parseInt(TEST_SONG_ID));
        });

        test('HEAD /api/song/:id - 应该检查歌曲可用性', async () => {
            await request(app)
                .head(`${API_BASE}/song/${TEST_SONG_ID}`)
                .expect(200);
        });
    });

    // 搜索API测试
    describe('搜索API', () => {
        test('POST /api/search - 关键词搜索', async () => {
            const response = await request(app)
                .post(`${API_BASE}/search`)
                .send({
                    type: 'keyword',
                    query: TEST_SEARCH_KEYWORD,
                    page: 1,
                    pageSize: 10
                })
                .expect(200);
            
            expect(response.body.code).toBe(200);
            expect(Array.isArray(response.body.data)).toBe(true);
        });

        test('GET /api/search/id/:songId - ID搜索', async () => {
            const response = await request(app)
                .get(`${API_BASE}/search/id/${TEST_SONG_ID}`)
                .expect(200);
            
            expect(response.body.code).toBe(200);
        });
    });

    // 解锁API测试
    describe('解锁API', () => {
        test('POST /api/unlock - 批量解锁', async () => {
            const response = await request(app)
                .post(`${API_BASE}/unlock`)
                .send({
                    songIds: [parseInt(TEST_SONG_ID)],
                    minBitrate: 128000
                })
                .expect(200);
            
            expect(response.body.code).toBe(200);
        });
    });

    // 音源管理API测试
    describe('音源管理API', () => {
        test('GET /api/sources - 获取音源列表', async () => {
            const response = await request(app)
                .get(`${API_BASE}/sources`)
                .expect(200);
            
            expect(response.body.code).toBe(200);
            expect(Array.isArray(response.body.data.sources)).toBe(true);
        });
    });

    // 错误处理测试
    describe('错误处理', () => {
        test('GET /api/song/invalid - 无效歌曲ID应返回400', async () => {
            await request(app)
                .get(`${API_BASE}/song/invalid`)
                .expect(400);
        });

        test('GET /api/nonexistent - 不存在的路径应返回404', async () => {
            await request(app)
                .get(`${API_BASE}/nonexistent`)
                .expect(404);
        });

        test('POST /api/search - 缺少必需参数应返回400', async () => {
            await request(app)
                .post(`${API_BASE}/search`)
                .send({})
                .expect(400);
        });
    });

    // 性能测试
    describe('性能测试', () => {
        test('API响应时间应在合理范围内', async () => {
            const start = Date.now();
            await request(app)
                .get('/health')
                .expect(200);
            const duration = Date.now() - start;
            
            expect(duration).toBeLessThan(1000); // 应在1秒内响应
        });
    });
});
