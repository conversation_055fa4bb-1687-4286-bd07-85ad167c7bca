#!/usr/bin/env node

/**
 * UnblockNeteaseMusic Backend - 运行时验证器
 * Runtime Validator for Music Unlock Service
 */

const { spawn } = require('child_process');
const http = require('http');
const fs = require('fs');

// 验证配置
const RUNTIME_CONFIG = {
    serverHost: 'localhost',
    serverPort: 50091,
    healthCheckTimeout: 15000,
    apiTestTimeout: 10000,
    testSongs: ['418602084', '186016'],
    testSources: ['migu', 'qq', 'kuwo'],
    endpoints: [
        { path: '/', method: 'GET', description: '主页API文档' },
        { path: '/music/source', method: 'GET', description: '音源管理服务' },
        { path: '/music/unlock?sources=migu&songs=418602084', method: 'GET', description: '单首歌曲解锁' },
        { path: '/music/unlock?sources=qq,migu&songs=418602084,186016', method: 'GET', description: '批量歌曲解锁' },
        { path: '/favicon.ico', method: 'GET', description: 'Favicon处理' }
    ]
};

// 颜色定义
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// 日志函数
function log(level, message) {
    const timestamp = new Date().toISOString();
    const color = colors[level] || colors.reset;
    console.log(`${color}[${level.toUpperCase()}]${colors.reset} ${timestamp} - ${message}`);
}

function logHeader(title) {
    console.log('\n' + colors.cyan + '='.repeat(60));
    console.log(`🚀 ${title}`);
    console.log('='.repeat(60) + colors.reset);
}

function logSuccess(message) {
    log('success', `✅ ${message}`);
}

function logError(message) {
    log('error', `❌ ${message}`);
}

function logInfo(message) {
    log('info', `ℹ️ ${message}`);
}

function logWarning(message) {
    log('warning', `⚠️ ${message}`);
}

// 运行时验证器
class RuntimeValidator {
    constructor() {
        this.validationResults = {
            serverStartup: false,
            healthCheck: false,
            apiEndpoints: false,
            errorHandling: false,
            performance: false
        };
        this.testResults = [];
        this.serverProcess = null;
    }

    // 检查服务器是否运行
    async checkServerRunning() {
        return new Promise((resolve) => {
            const req = http.request({
                hostname: RUNTIME_CONFIG.serverHost,
                port: RUNTIME_CONFIG.serverPort,
                path: '/',
                method: 'GET',
                timeout: 5000
            }, (res) => {
                resolve(true);
            });
            
            req.on('error', () => {
                resolve(false);
            });
            
            req.on('timeout', () => {
                req.destroy();
                resolve(false);
            });
            
            req.end();
        });
    }

    // 启动服务器
    async startServer() {
        logHeader('服务器启动验证');
        
        // 检查服务器是否已经运行
        const isRunning = await this.checkServerRunning();
        if (isRunning) {
            logSuccess('服务器已经在运行');
            this.validationResults.serverStartup = true;
            return true;
        }
        
        logInfo('启动服务器...');
        
        return new Promise((resolve) => {
            this.serverProcess = spawn('npm', ['start'], {
                stdio: 'pipe',
                shell: true
            });
            
            let started = false;
            
            this.serverProcess.stdout.on('data', (data) => {
                const output = data.toString();
                if (output.includes('音乐解锁服务启动成功') && !started) {
                    started = true;
                    logSuccess('服务器启动成功');
                    this.validationResults.serverStartup = true;
                    resolve(true);
                }
            });
            
            this.serverProcess.stderr.on('data', (data) => {
                logWarning(`服务器警告: ${data.toString()}`);
            });
            
            this.serverProcess.on('error', (error) => {
                logError(`服务器启动失败: ${error.message}`);
                resolve(false);
            });
            
            // 超时处理
            setTimeout(() => {
                if (!started) {
                    logError('服务器启动超时');
                    resolve(false);
                }
            }, RUNTIME_CONFIG.healthCheckTimeout);
        });
    }

    // 健康检查
    async performHealthCheck() {
        logHeader('健康检查');
        
        try {
            const response = await this.makeHttpRequest('/', 'GET');
            
            if (response.statusCode === 200) {
                logSuccess('健康检查通过');
                logInfo(`响应时间: ${response.responseTime}ms`);
                this.validationResults.healthCheck = true;
                return true;
            } else {
                logError(`健康检查失败: HTTP ${response.statusCode}`);
                return false;
            }
            
        } catch (error) {
            logError(`健康检查失败: ${error.message}`);
            return false;
        }
    }

    // 测试API端点
    async testApiEndpoints() {
        logHeader('API端点测试');
        
        let allPassed = true;
        
        for (const endpoint of RUNTIME_CONFIG.endpoints) {
            try {
                logInfo(`测试: ${endpoint.description}`);
                const response = await this.makeHttpRequest(endpoint.path, endpoint.method);
                
                const testResult = {
                    endpoint: endpoint.path,
                    method: endpoint.method,
                    description: endpoint.description,
                    statusCode: response.statusCode,
                    responseTime: response.responseTime,
                    success: response.statusCode >= 200 && response.statusCode < 400
                };
                
                this.testResults.push(testResult);
                
                if (testResult.success) {
                    logSuccess(`${endpoint.description}: HTTP ${response.statusCode} (${response.responseTime}ms)`);
                } else {
                    logError(`${endpoint.description}: HTTP ${response.statusCode}`);
                    allPassed = false;
                }
                
            } catch (error) {
                logError(`${endpoint.description}: ${error.message}`);
                allPassed = false;
                
                this.testResults.push({
                    endpoint: endpoint.path,
                    method: endpoint.method,
                    description: endpoint.description,
                    statusCode: 0,
                    responseTime: 0,
                    success: false,
                    error: error.message
                });
            }
        }
        
        this.validationResults.apiEndpoints = allPassed;
        return allPassed;
    }

    // 测试错误处理
    async testErrorHandling() {
        logHeader('错误处理测试');
        
        try {
            // 测试无效参数
            logInfo('测试无效参数处理...');
            const invalidResponse = await this.makeHttpRequest('/music/unlock?sources=migu&songs=invalid123', 'GET');
            
            if (invalidResponse.statusCode === 400) {
                logSuccess('无效参数错误处理正确');
            } else {
                logWarning(`无效参数处理异常: HTTP ${invalidResponse.statusCode}`);
            }
            
            // 测试不存在的路径
            logInfo('测试404错误处理...');
            const notFoundResponse = await this.makeHttpRequest('/nonexistent-path', 'GET');
            
            if (notFoundResponse.statusCode === 404) {
                logSuccess('404错误处理正确');
            } else {
                logWarning(`404处理异常: HTTP ${notFoundResponse.statusCode}`);
            }
            
            this.validationResults.errorHandling = true;
            return true;
            
        } catch (error) {
            logError(`错误处理测试失败: ${error.message}`);
            return false;
        }
    }

    // 性能测试
    async testPerformance() {
        logHeader('性能测试');
        
        try {
            const performanceTests = [];
            
            // 并发请求测试
            logInfo('执行并发请求测试...');
            const concurrentPromises = [];
            
            for (let i = 0; i < 5; i++) {
                concurrentPromises.push(this.makeHttpRequest('/', 'GET'));
            }
            
            const concurrentResults = await Promise.all(concurrentPromises);
            const avgResponseTime = concurrentResults.reduce((sum, result) => sum + result.responseTime, 0) / concurrentResults.length;
            
            logInfo(`并发请求平均响应时间: ${avgResponseTime.toFixed(2)}ms`);
            
            if (avgResponseTime < 1000) {
                logSuccess('性能测试通过');
                this.validationResults.performance = true;
                return true;
            } else {
                logWarning('响应时间较慢，建议优化');
                return false;
            }
            
        } catch (error) {
            logError(`性能测试失败: ${error.message}`);
            return false;
        }
    }

    // HTTP请求工具
    makeHttpRequest(path, method = 'GET') {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const req = http.request({
                hostname: RUNTIME_CONFIG.serverHost,
                port: RUNTIME_CONFIG.serverPort,
                path: path,
                method: method,
                timeout: RUNTIME_CONFIG.apiTestTimeout
            }, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    const responseTime = Date.now() - startTime;
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        responseTime: responseTime
                    });
                });
            });
            
            req.on('error', (error) => {
                reject(error);
            });
            
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });
            
            req.end();
        });
    }

    // 生成验证报告
    generateValidationReport() {
        logHeader('生成运行验证报告');
        
        const timestamp = new Date().toISOString();
        const reportPath = `runtime-validation-report-${timestamp.replace(/[:.]/g, '-')}.md`;
        
        const allPassed = Object.values(this.validationResults).every(result => result);
        const passedCount = Object.values(this.validationResults).filter(result => result).length;
        const totalCount = Object.keys(this.validationResults).length;
        
        const report = `# 🚀 运行验证报告

## 📊 验证摘要
- **验证时间**: ${new Date().toLocaleString()}
- **服务器地址**: http://${RUNTIME_CONFIG.serverHost}:${RUNTIME_CONFIG.serverPort}
- **验证通过率**: ${((passedCount / totalCount) * 100).toFixed(2)}%

## 🎯 验证结果
- **服务器启动**: ${this.validationResults.serverStartup ? '✅ 成功' : '❌ 失败'}
- **健康检查**: ${this.validationResults.healthCheck ? '✅ 通过' : '❌ 失败'}
- **API端点**: ${this.validationResults.apiEndpoints ? '✅ 正常' : '❌ 异常'}
- **错误处理**: ${this.validationResults.errorHandling ? '✅ 正确' : '❌ 错误'}
- **性能测试**: ${this.validationResults.performance ? '✅ 良好' : '❌ 需优化'}

## 📈 API测试详情

${this.testResults.map(result => 
    `### ${result.description}
- **端点**: ${result.method} ${result.endpoint}
- **状态码**: ${result.statusCode}
- **响应时间**: ${result.responseTime}ms
- **结果**: ${result.success ? '✅ 成功' : '❌ 失败'}
${result.error ? `- **错误**: ${result.error}` : ''}`
).join('\n\n')}

## 📝 总体评估
${allPassed ? 
    '✅ **优秀** - 所有运行验证项目通过，服务运行稳定。' : 
    `⚠️ **需要关注** - ${totalCount - passedCount}个验证项目失败，需要检查问题。`
}

## 🚀 服务状态
- **服务地址**: http://${RUNTIME_CONFIG.serverHost}:${RUNTIME_CONFIG.serverPort}
- **API文档**: http://${RUNTIME_CONFIG.serverHost}:${RUNTIME_CONFIG.serverPort}/
- **音源管理**: http://${RUNTIME_CONFIG.serverHost}:${RUNTIME_CONFIG.serverPort}/music/source
- **解锁服务**: http://${RUNTIME_CONFIG.serverHost}:${RUNTIME_CONFIG.serverPort}/music/unlock

## 🔧 使用建议
- 定期运行运行验证确保服务稳定
- 监控API响应时间和错误率
- 及时处理错误日志和异常情况
- 保持服务配置的最新状态

---
*报告生成时间: ${timestamp}*
`;
        
        fs.writeFileSync(reportPath, report);
        logSuccess(`运行验证报告已生成: ${reportPath}`);
        
        return reportPath;
    }

    // 清理资源
    cleanup() {
        if (this.serverProcess) {
            logInfo('清理服务器进程...');
            this.serverProcess.kill();
        }
    }

    // 运行完整验证
    async runFullValidation() {
        logHeader('UnblockNeteaseMusic Backend - 运行验证器');
        
        try {
            // 执行所有验证
            await this.startServer();
            
            // 等待服务器完全启动
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            await this.performHealthCheck();
            await this.testApiEndpoints();
            await this.testErrorHandling();
            await this.testPerformance();
            
            // 生成报告
            const reportPath = this.generateValidationReport();
            
            // 显示摘要
            const allPassed = Object.values(this.validationResults).every(result => result);
            const passedCount = Object.values(this.validationResults).filter(result => result).length;
            const totalCount = Object.keys(this.validationResults).length;
            
            logHeader('运行验证完成');
            logInfo(`验证报告: ${reportPath}`);
            logInfo(`验证通过率: ${((passedCount / totalCount) * 100).toFixed(2)}%`);
            
            if (allPassed) {
                logSuccess('🎉 所有运行验证项目通过！');
                return true;
            } else {
                logWarning(`⚠️ ${totalCount - passedCount}个验证项目失败`);
                return false;
            }
            
        } catch (error) {
            logError(`运行验证失败: ${error.message}`);
            return false;
        }
    }
}

// 主函数
async function main() {
    const validator = new RuntimeValidator();
    
    try {
        const success = await validator.runFullValidation();
        process.exit(success ? 0 : 1);
        
    } catch (error) {
        logError(`运行验证器错误: ${error.message}`);
        process.exit(1);
    } finally {
        validator.cleanup();
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { RuntimeValidator };
