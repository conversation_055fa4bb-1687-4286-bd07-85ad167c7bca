✅ API文档访问 - 状态码: 200
响应: {"状态码":200,"消息":"音乐解锁服务API文档","时间戳":"2025-08-02T12:22:50.300Z","版本":"2.0.0","API文档":[{"快速示例":{"单首歌曲解...

✅ 正常参数 - 状态码: 200
响应: {"状态码":200,"消息":"单首歌曲解锁完成","时间戳":"2025-08-02T12:22:51.208Z","音源配置来源":"环境变量","使用的音源":["migu","kuwo","...

✅ 未知参数过滤 - 状态码: 200
响应: {"状态码":200,"消息":"单首歌曲解锁完成","时间戳":"2025-08-02T12:22:51.499Z","音源配置来源":"环境变量","使用的音源":["migu","kuwo","...

✅ 无效songs格式 - 状态码: 400
响应: {"状态码":400,"消息":"\"songs\" with value \"invalid\" fails to match the required pattern: /^[\\d,]+$/",...

✅ 缺少必需参数 - 状态码: 400
响应: {"状态码":400,"消息":"\"songs\" is required","时间戳":"2025-08-02T12:22:51.908Z","数据":null,"错误代码":"VALIDATIO...

✅ 单首歌曲解锁 - 状态码: 200
响应: {"状态码":200,"消息":"单首歌曲解锁完成","时间戳":"2025-08-02T12:22:52.773Z","音源配置来源":"环境变量","使用的音源":["migu","kuwo","...

✅ 批量歌曲解锁 - 状态码: 200
响应: {"状态码":200,"消息":"批量解锁完成","时间戳":"2025-08-02T12:22:53.652Z","音源配置来源":"环境变量","使用的音源":["migu","kuwo","qq...

✅ 获取音源信息 - 状态码: 200
响应: {"状态码":200,"消息":"音源管理服务","时间戳":"2025-08-02T12:22:53.837Z","启用无损音质":true,"启用本地VIP":true,"严格按音源顺序":tru...

