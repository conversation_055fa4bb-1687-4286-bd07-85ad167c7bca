/**
 * 音乐解锁服务模块
 * 基于UnblockNeteaseMusic实现的音乐解锁核心服务
 */

const match = require('@unblockneteasemusic/server');
const config = require('../config/config');
const { logError, logPerformance, logBusiness } = require('../middleware/logger');
const { ServiceUnavailableError, NotFoundError } = require('../middleware/errorHandler');
const { SOURCE_DISPLAY_NAMES } = require('../utils/constants');

/**
 * 设置UnblockNeteaseMusic全局配置
 * 只在用户明确配置时才设置环境变量，否则让库使用默认行为
 */
function setupGlobalConfig() {
    // 设置UnblockNeteaseMusic库的日志级别，减少网络警告噪音
    // 将WARN级别的网络超时警告设置为error级别，只显示真正的错误
    if (!process.env.LOG_LEVEL) {
        process.env.LOG_LEVEL = 'error'; // 只显示错误级别日志，过滤网络超时警告
    }

    // 设置音源相关的环境变量 (Cookie认证)
    if (config.music.neteaseCookie) {
        process.env.NETEASE_COOKIE = config.music.neteaseCookie;
    }
    if (config.music.qqCookie) {
        process.env.QQ_COOKIE = config.music.qqCookie;
    }
    if (config.music.miguCookie) {
        process.env.MIGU_COOKIE = config.music.miguCookie;
    }
    if (config.music.jooxCookie) {
        process.env.JOOX_COOKIE = config.music.jooxCookie;
    }
    if (config.music.youtubeKey) {
        process.env.YOUTUBE_KEY = config.music.youtubeKey;
    }

    // 功能开关：只在用户明确配置时才设置，否则使用库默认值
    // 库默认值：ENABLE_FLAC=false, ENABLE_LOCAL_VIP=false, BLOCK_ADS=false, FOLLOW_SOURCE_ORDER=false, SELECT_MAX_BR=false
    if (config.music.enableFlac) {
        process.env.ENABLE_FLAC = config.music.enableFlac;
    }
    if (config.music.enableLocalVip) {
        process.env.ENABLE_LOCAL_VIP = config.music.enableLocalVip;
    }
    if (config.music.followSourceOrder) {
        process.env.FOLLOW_SOURCE_ORDER = config.music.followSourceOrder;
    }
    if (config.music.blockAds) {
        process.env.BLOCK_ADS = config.music.blockAds;
    }
    if (config.music.selectMaxBr) {
        process.env.SELECT_MAX_BR = config.music.selectMaxBr;
    }
}

// 初始化全局配置
setupGlobalConfig();

/**
 * 解锁单首歌曲
 * @param {number|string} songId - 网易云音乐歌曲ID
 * @param {Array<string>} sources - 指定的音源列表，默认使用配置中的音源
 * @returns {Promise<Object>} 解锁结果
 */
async function unlockSong(songId, sources = null) {
    const startTime = Date.now();
    const useSources = sources || config.music.sources;

    try {
        logBusiness('开始解锁歌曲', { songId, sources: useSources });

        // 验证歌曲ID
        const numericSongId = parseInt(songId);
        if (isNaN(numericSongId) || numericSongId <= 0) {
            throw new Error(`无效的歌曲ID: ${songId}`);
        }

        // 调用UnblockNeteaseMusic的match函数 (使用配置化超时)
        const result = await Promise.race([
            match(numericSongId, useSources),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('解锁请求超时')), config.performance.timeout)
            )
        ]);

        const duration = Date.now() - startTime;
        logPerformance('歌曲解锁', duration, { songId, success: !!result });

        if (!result) {
            throw new NotFoundError(`歌曲 ${songId} 未找到可用音源`);
        }

        // 格式化返回结果
        const formattedResult = formatUnlockResult(result, songId);
    
        logBusiness('歌曲解锁成功', {
            songId,
            source: formattedResult.音源ID,
            bitrate: formattedResult.音质
        });

        return formattedResult;

    } catch (error) {
        const duration = Date.now() - startTime;
        logPerformance('歌曲解锁失败', duration, { songId, error: error.message });

        // 处理不同类型的错误
        if (error instanceof NotFoundError) {
            throw error;
        }

        // 检查是否是"歌曲不可用"的正常业务情况
        const isSongNotAvailable = error.name === 'SongNotAvailable' ||
            error.message?.includes('not available in any source') ||
            error.message?.includes('not available in');

        if (isSongNotAvailable) {
            // 这是正常的业务情况，不记录为系统错误，只记录业务日志
            logBusiness('音源解锁失败', {
                songId,
                source: useSources.join(','),
                error: '歌曲在指定音源中不可用'
            });

            throw new ServiceUnavailableError(`歌曲 ${songId} 在指定音源中不可用`);
        }

        // 只有真正的系统错误才记录错误日志和堆栈
        const errorContext = error.message === '解锁请求超时' ? 'unlock_song_timeout' :
            error.message?.includes('网络') ? 'unlock_song_network' : 'unlock_song';
        logError(error, { context: errorContext, songId, sources: useSources, duration });

        // 根据错误类型返回相应的错误信息
        const errorMessage = error.message === '解锁请求超时' ? `歌曲 ${songId} 解锁超时，请稍后重试` :
            error.message?.includes('网络') ? `网络连接异常，无法解锁歌曲 ${songId}` :
                `解锁歌曲 ${songId} 失败: ${error.message}`;

        throw new ServiceUnavailableError(errorMessage);
    }
}

// 批量解锁功能已移至路由层实现

/**
 * 格式化解锁结果
 * @param {Object} rawResult - UnblockNeteaseMusic返回的原始结果
 * @param {number|string} songId - 歌曲ID
 * @returns {Object} 格式化后的结果
 */
function formatUnlockResult(rawResult, songId) {
    return {
        歌曲ID: parseInt(songId),
        播放链接: rawResult.url,
        音源ID: rawResult.source || 'unknown',
        音源名称: SOURCE_DISPLAY_NAMES[rawResult.source] || rawResult.source,
        音质: rawResult.br || rawResult.bitrate || 0,
        文件大小: rawResult.size || 0,
        格式: rawResult.type || getFileTypeFromUrl(rawResult.url),
        解锁时间: new Date().toISOString()
    };
}

/**
 * 从URL推断文件类型
 * @param {string} url - 音乐文件URL
 * @returns {string} 文件类型
 */
function getFileTypeFromUrl(url) {
    if (!url || typeof url !== 'string') return 'unknown';

    try {
        const urlParts = url.split('.');
        if (urlParts.length < 2) return 'mp3'; // 默认为mp3

        const extension = urlParts.pop().toLowerCase();
        const typeMap = {
            'mp3': 'mp3',
            'flac': 'flac',
            'm4a': 'm4a',
            'aac': 'aac',
            'ogg': 'ogg',
            'wav': 'wav'
        };

        return typeMap[extension] || 'mp3';
    } catch (error) {
        logError(error, { context: 'get_file_type_from_url', url });
        return 'mp3'; // 出错时返回默认值
    }
}

// 音源管理功能已移至路由层实现

module.exports = {
    unlockSong,
    formatUnlockResult
};
