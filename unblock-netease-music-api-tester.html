<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UnblockNeteaseMusic API 专业测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .card-body {
            padding: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .btn-secondary:hover {
            box-shadow: 0 4px 12px rgba(252, 182, 159, 0.4);
        }

        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .api-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #4facfe;
        }

        .api-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .api-url {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            word-break: break-all;
        }

        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .help-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .help-title {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .help-list {
            list-style: none;
            padding-left: 0;
        }

        .help-list li {
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }

        .help-list li:before {
            content: "•";
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .api-grid {
                grid-template-columns: 1fr;
            }
            
            .card-body {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 UnblockNeteaseMusic API 测试工具</h1>
            <p>专业的音乐解锁服务API测试平台</p>
        </div>

        <!-- 服务器配置 -->
        <div class="card">
            <div class="card-header">
                <span class="status-indicator status-success"></span>
                服务器配置
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">API服务器地址</label>
                    <input type="text" id="serverUrl" class="form-input" value="http://localhost:50090" placeholder="请输入服务器地址">
                </div>
                <button class="btn" onclick="testConnection()">
                    <span id="connectionStatus">测试连接</span>
                </button>
                <button class="btn btn-secondary" onclick="openServer()">打开服务器</button>
            </div>
        </div>

        <!-- API测试区域 -->
        <div class="api-grid">
            <!-- 健康检查 -->
            <div class="card">
                <div class="card-header">健康检查</div>
                <div class="card-body">
                    <div class="api-item">
                        <div class="api-title">服务状态检查</div>
                        <div class="api-url">GET /</div>
                        <button class="btn" onclick="testHealthCheck()">测试健康检查</button>
                    </div>
                </div>
            </div>

            <!-- 音乐解锁 -->
            <div class="card">
                <div class="card-header">音乐解锁</div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="form-label">歌曲ID (多个用逗号分隔)</label>
                        <input type="text" id="songIds" class="form-input" value="418602084" placeholder="例如: 418602084,123456">
                    </div>
                    <button class="btn" onclick="testMusicUnlock()">解锁音乐</button>
                    <button class="btn btn-secondary" onclick="testBatchUnlock()">批量测试</button>
                </div>
            </div>

            <!-- 音源管理 -->
            <div class="card">
                <div class="card-header">音源管理</div>
                <div class="card-body">
                    <div class="api-item">
                        <div class="api-title">获取音源信息</div>
                        <div class="api-url">GET /music/source</div>
                        <button class="btn" onclick="testSourceManagement()">查看音源</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 自定义测试 -->
        <div class="card">
            <div class="card-header">自定义API测试</div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">API路径</label>
                    <input type="text" id="customPath" class="form-input" placeholder="例如: /music/status">
                </div>
                <div class="form-group">
                    <label class="form-label">查询参数 (可选)</label>
                    <input type="text" id="customParams" class="form-input" placeholder="例如: songs=123456&sources=qq">
                </div>
                <button class="btn" onclick="testCustomApi()">测试自定义API</button>
                <button class="btn btn-secondary" onclick="copyUrl()">复制URL</button>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div class="card">
            <div class="card-header">测试结果</div>
            <div class="card-body">
                <div id="resultArea" class="result-area">
点击上方按钮开始测试API...

💡 提示：由于浏览器CORS限制，测试结果将在新窗口中显示
                </div>
            </div>
        </div>

        <!-- 帮助信息 -->
        <div class="help-section">
            <div class="help-title">📖 使用说明</div>
            <ul class="help-list">
                <li>确保UnblockNeteaseMusic服务已启动并运行在指定端口</li>
                <li>由于浏览器CORS安全限制，API测试结果会在新窗口中显示</li>
                <li>可以使用"复制URL"功能获取完整的API请求地址</li>
                <li>支持批量音乐解锁，多个歌曲ID用逗号分隔</li>
                <li>所有API响应均为JSON格式，包含详细的状态信息</li>
            </ul>
        </div>
    </div>

    <script>
        // 全局配置
        const CONFIG = {
            defaultServer: 'http://localhost:50090',
            testSongIds: ['418602084', '123456', '186016'],
            corsMessage: '由于CORS限制，结果将在新窗口显示'
        };

        // 工具函数
        function getServerUrl() {
            return document.getElementById('serverUrl').value.trim() || CONFIG.defaultServer;
        }

        function updateResult(message, type = 'info') {
            const resultArea = document.getElementById('resultArea');
            const timestamp = new Date().toLocaleString('zh-CN');
            const statusIcon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';

            resultArea.textContent = `[${timestamp}] ${statusIcon} ${message}`;
            resultArea.scrollTop = resultArea.scrollHeight;
        }

        function showLoading(elementId, originalText) {
            const element = document.getElementById(elementId);
            element.innerHTML = '<span class="loading"></span>正在测试...';

            setTimeout(() => {
                element.textContent = originalText;
            }, 2000);
        }

        function buildUrl(path, params = {}) {
            const baseUrl = getServerUrl();
            const url = new URL(path, baseUrl);

            Object.keys(params).forEach(key => {
                if (params[key]) {
                    url.searchParams.append(key, params[key]);
                }
            });

            return url.toString();
        }

        function openUrlInNewWindow(url, description) {
            updateResult(`正在打开: ${description}\nURL: ${url}\n${CONFIG.corsMessage}`, 'info');
            window.open(url, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        }

        // API测试函数
        function testConnection() {
            showLoading('connectionStatus', '测试连接');
            const url = buildUrl('/');
            updateResult(`测试服务器连接: ${getServerUrl()}`, 'info');
            openUrlInNewWindow(url, '服务器连接测试');
        }

        function openServer() {
            const url = getServerUrl();
            updateResult(`打开服务器管理页面: ${url}`, 'info');
            window.open(url, '_blank');
        }

        function testHealthCheck() {
            const url = buildUrl('/');
            updateResult('执行健康检查...', 'info');
            openUrlInNewWindow(url, '健康检查 - 获取API文档和服务状态');
        }

        function testMusicUnlock() {
            const songIds = document.getElementById('songIds').value.trim();

            if (!songIds) {
                updateResult('请输入歌曲ID', 'error');
                return;
            }

            // 验证歌曲ID格式
            const ids = songIds.split(',').map(id => id.trim()).filter(id => id);
            const invalidIds = ids.filter(id => !/^\d+$/.test(id));

            if (invalidIds.length > 0) {
                updateResult(`无效的歌曲ID格式: ${invalidIds.join(', ')}`, 'error');
                return;
            }

            const url = buildUrl('/music/unlock', { songs: songIds });
            updateResult(`测试音乐解锁 - 歌曲数量: ${ids.length}`, 'info');
            openUrlInNewWindow(url, `音乐解锁 - ${ids.length}首歌曲`);
        }

        function testBatchUnlock() {
            const batchIds = CONFIG.testSongIds.join(',');
            document.getElementById('songIds').value = batchIds;

            const url = buildUrl('/music/unlock', { songs: batchIds });
            updateResult(`批量测试 - 使用预设歌曲ID: ${batchIds}`, 'info');
            openUrlInNewWindow(url, '批量音乐解锁测试');
        }

        function testSourceManagement() {
            const url = buildUrl('/music/source');
            updateResult('获取音源管理信息...', 'info');
            openUrlInNewWindow(url, '音源管理 - 查看所有可用音源');
        }

        function testCustomApi() {
            const path = document.getElementById('customPath').value.trim();
            const params = document.getElementById('customParams').value.trim();

            if (!path) {
                updateResult('请输入API路径', 'error');
                return;
            }

            // 确保路径以/开头
            const apiPath = path.startsWith('/') ? path : '/' + path;

            // 解析查询参数
            const queryParams = {};
            if (params) {
                params.split('&').forEach(param => {
                    const [key, value] = param.split('=');
                    if (key && value) {
                        queryParams[key.trim()] = decodeURIComponent(value.trim());
                    }
                });
            }

            const url = buildUrl(apiPath, queryParams);
            updateResult(`自定义API测试: ${apiPath}`, 'info');
            openUrlInNewWindow(url, `自定义API - ${apiPath}`);
        }

        function copyUrl() {
            const path = document.getElementById('customPath').value.trim();
            const params = document.getElementById('customParams').value.trim();

            if (!path) {
                updateResult('请先输入API路径', 'error');
                return;
            }

            const apiPath = path.startsWith('/') ? path : '/' + path;
            const queryParams = {};

            if (params) {
                params.split('&').forEach(param => {
                    const [key, value] = param.split('=');
                    if (key && value) {
                        queryParams[key.trim()] = decodeURIComponent(value.trim());
                    }
                });
            }

            const url = buildUrl(apiPath, queryParams);

            // 复制到剪贴板
            navigator.clipboard.writeText(url).then(() => {
                updateResult(`URL已复制到剪贴板:\n${url}`, 'success');
            }).catch(() => {
                updateResult(`URL复制失败，请手动复制:\n${url}`, 'error');
            });
        }

        // 快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'Enter':
                        e.preventDefault();
                        testHealthCheck();
                        break;
                    case '1':
                        e.preventDefault();
                        testMusicUnlock();
                        break;
                    case '2':
                        e.preventDefault();
                        testSourceManagement();
                        break;
                }
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateResult('🎵 UnblockNeteaseMusic API测试工具已就绪\n\n快捷键:\nCtrl+Enter: 健康检查\nCtrl+1: 音乐解锁\nCtrl+2: 音源管理', 'success');

            // 自动检测服务器状态
            setTimeout(() => {
                const serverUrl = getServerUrl();
                updateResult(`当前服务器: ${serverUrl}\n点击"测试连接"验证服务器状态`, 'info');
            }, 1000);
        });

        // 表单回车提交支持
        document.getElementById('songIds').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testMusicUnlock();
            }
        });

        document.getElementById('customPath').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testCustomApi();
            }
        });

        document.getElementById('serverUrl').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testConnection();
            }
        });
    </script>
</body>
</html>
