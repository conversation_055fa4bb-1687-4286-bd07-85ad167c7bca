/**
 * API简化重构后的全面测试脚本
 * 验证移除sources参数后的API功能完整性
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:50090';
const TEST_TIMEOUT = 30000;

// 测试配置
const TEST_CONFIG = {
    testSongs: [
        { id: *********, name: '说走就走', expectedSource: 'migu' },
        { id: 186016, name: '青花瓷', expectedSource: 'migu' }
    ],
    invalidSongs: [*********, 'invalid'],
    batchSongs: [*********, 186016]
};

class APITester {
    constructor() {
        this.results = {
            passed: 0,
            failed: 0,
            tests: []
        };
    }

    async runTest(testName, testFn) {
        console.log(`\n🧪 测试: ${testName}`);
        console.log('=' .repeat(60));
        
        try {
            const startTime = Date.now();
            await testFn();
            const duration = Date.now() - startTime;
            
            console.log(`✅ 通过 (${duration}ms)`);
            this.results.passed++;
            this.results.tests.push({ name: testName, status: 'PASSED', duration });
        } catch (error) {
            console.log(`❌ 失败: ${error.message}`);
            this.results.failed++;
            this.results.tests.push({ name: testName, status: 'FAILED', error: error.message });
        }
    }

    async testSimplifiedUnlockAPI() {
        // 测试简化后的单首歌曲解锁
        const response = await axios.get(`${BASE_URL}/music/unlock`, {
            params: { songs: TEST_CONFIG.testSongs[0].id },
            timeout: TEST_TIMEOUT
        });

        if (response.status !== 200) {
            throw new Error(`期望状态码200，实际${response.status}`);
        }

        const data = response.data;
        if (data.解锁成功 !== 1) {
            throw new Error(`期望解锁成功1首，实际${data.解锁成功}首`);
        }

        if (data.音源配置来源 !== '环境变量') {
            throw new Error(`期望音源配置来源为'环境变量'，实际'${data.音源配置来源}'`);
        }

        console.log(`  📊 解锁成功率: ${data.解锁成功率}`);
        console.log(`  🎵 使用音源: ${data.使用的音源.join(', ')}`);
        console.log(`  🎯 配置来源: ${data.音源配置来源}`);
    }

    async testBatchUnlock() {
        // 测试批量解锁
        const songIds = TEST_CONFIG.batchSongs.join(',');
        const response = await axios.get(`${BASE_URL}/music/unlock`, {
            params: { songs: songIds },
            timeout: TEST_TIMEOUT
        });

        const data = response.data;
        if (data.解锁总数 !== TEST_CONFIG.batchSongs.length) {
            throw new Error(`期望解锁总数${TEST_CONFIG.batchSongs.length}，实际${data.解锁总数}`);
        }

        console.log(`  📊 批量解锁: ${data.解锁成功}/${data.解锁总数} (${data.解锁成功率})`);
        console.log(`  🎵 使用音源: ${data.使用的音源.join(', ')}`);
    }

    async testSourcesParameterRejection() {
        // 测试sources参数被正确拒绝
        try {
            await axios.get(`${BASE_URL}/music/unlock`, {
                params: { sources: 'qq', songs: '*********' },
                timeout: TEST_TIMEOUT
            });
            throw new Error('应该拒绝sources参数但没有拒绝');
        } catch (error) {
            if (error.response && error.response.status === 400) {
                const errorMsg = error.response.data.消息;
                if (errorMsg.includes('sources') && errorMsg.includes('not allowed')) {
                    console.log(`  ✅ 正确拒绝sources参数: ${errorMsg}`);
                    return;
                }
            }
            throw new Error(`期望400错误拒绝sources参数，实际: ${error.message}`);
        }
    }

    async testInvalidSongs() {
        // 测试无效歌曲ID处理
        try {
            await axios.get(`${BASE_URL}/music/unlock`, {
                params: { songs: 'invalid_id' },
                timeout: TEST_TIMEOUT
            });
            throw new Error('应该拒绝无效歌曲ID但没有拒绝');
        } catch (error) {
            if (error.response && error.response.status === 400) {
                console.log(`  ✅ 正确拒绝无效歌曲ID: ${error.response.data.消息}`);
                return;
            }
            throw new Error(`期望400错误拒绝无效ID，实际: ${error.message}`);
        }
    }

    async testSourceManagementAPI() {
        // 测试音源管理API
        const response = await axios.get(`${BASE_URL}/music/source`, {
            timeout: TEST_TIMEOUT
        });

        const data = response.data;
        if (response.status !== 200) {
            throw new Error(`期望状态码200，实际${response.status}`);
        }

        if (!data.音源总数 || data.音源总数 < 1) {
            throw new Error('音源总数应该大于0');
        }

        console.log(`  📊 音源总数: ${data.音源总数}`);
        console.log(`  🎵 已启用音源: ${data.已启用音源}`);
        console.log(`  ⚙️ 选择最高音质: ${data.选择最高音质}`);
    }

    async testPerformanceAndStability() {
        // 性能和稳定性测试
        const concurrentRequests = 3;
        const promises = [];

        for (let i = 0; i < concurrentRequests; i++) {
            promises.push(
                axios.get(`${BASE_URL}/music/unlock`, {
                    params: { songs: TEST_CONFIG.testSongs[0].id },
                    timeout: TEST_TIMEOUT
                })
            );
        }

        const results = await Promise.all(promises);
        const allSuccessful = results.every(r => r.status === 200 && r.data.解锁成功 >= 0);

        if (!allSuccessful) {
            throw new Error('并发请求测试失败');
        }

        console.log(`  🚀 并发请求测试: ${concurrentRequests}个请求全部成功`);
        console.log(`  ⏱️ 平均响应时间: ${results.reduce((sum, r) => sum + (r.config.metadata?.endTime - r.config.metadata?.startTime || 0), 0) / results.length}ms`);
    }

    async runAllTests() {
        console.log('🎯 开始API简化重构后的全面测试');
        console.log('=' .repeat(80));

        await this.runTest('简化解锁API测试', () => this.testSimplifiedUnlockAPI());
        await this.runTest('批量解锁测试', () => this.testBatchUnlock());
        await this.runTest('Sources参数拒绝测试', () => this.testSourcesParameterRejection());
        await this.runTest('无效歌曲ID测试', () => this.testInvalidSongs());
        await this.runTest('音源管理API测试', () => this.testSourceManagementAPI());
        await this.runTest('性能稳定性测试', () => this.testPerformanceAndStability());

        this.printSummary();
    }

    printSummary() {
        console.log('\n' + '=' .repeat(80));
        console.log('📋 测试总结报告');
        console.log('=' .repeat(80));
        console.log(`✅ 通过: ${this.results.passed}`);
        console.log(`❌ 失败: ${this.results.failed}`);
        console.log(`📊 成功率: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);
        
        if (this.results.failed > 0) {
            console.log('\n❌ 失败的测试:');
            this.results.tests.filter(t => t.status === 'FAILED').forEach(test => {
                console.log(`  - ${test.name}: ${test.error}`);
            });
        }

        console.log('\n🎉 API简化重构验证完成！');
    }
}

// 运行测试
async function main() {
    const tester = new APITester();
    
    try {
        await tester.runAllTests();
    } catch (error) {
        console.error('测试运行失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = APITester;
