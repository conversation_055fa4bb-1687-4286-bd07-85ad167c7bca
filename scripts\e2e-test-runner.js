#!/usr/bin/env node

/**
 * UnblockNeteaseMusic Backend - E2E测试运行器
 * End-to-End Test Runner for Music Unlock Service
 */

const { spawn } = require('child_process');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// E2E测试配置
const E2E_CONFIG = {
    server: {
        baseUrl: 'http://localhost:50091',
        startupTimeout: 30000,
        healthCheckInterval: 1000
    },
    playwright: {
        timeout: 60000,
        retries: 2,
        browsers: ['chromium'],
        headless: true
    },
    testSuites: [
        'tests/e2e-html.test.js',
        'tests/e2e-api.test.js'
    ]
};

// 颜色定义
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// 日志函数
function log(level, message) {
    const timestamp = new Date().toISOString();
    const color = colors[level] || colors.reset;
    console.log(`${color}[${level.toUpperCase()}]${colors.reset} ${timestamp} - ${message}`);
}

function logHeader(title) {
    console.log('\n' + colors.cyan + '='.repeat(60));
    console.log(`🌐 ${title}`);
    console.log('='.repeat(60) + colors.reset);
}

function logSuccess(message) {
    log('success', `✅ ${message}`);
}

function logError(message) {
    log('error', `❌ ${message}`);
}

function logInfo(message) {
    log('info', `ℹ️ ${message}`);
}

function logWarning(message) {
    log('warning', `⚠️ ${message}`);
}

// E2E测试管理器
class E2ETestManager {
    constructor() {
        this.serverProcess = null;
        this.testResults = [];
    }

    // 检查服务器状态
    async checkServerHealth() {
        try {
            const response = await axios.get(`${E2E_CONFIG.server.baseUrl}/health`, {
                timeout: 5000
            });
            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    // 等待服务器启动
    async waitForServer() {
        logInfo('等待服务器启动...');
        
        const startTime = Date.now();
        const timeout = E2E_CONFIG.server.startupTimeout;
        
        while (Date.now() - startTime < timeout) {
            if (await this.checkServerHealth()) {
                logSuccess('服务器已启动并响应正常');
                return true;
            }
            
            await new Promise(resolve => setTimeout(resolve, E2E_CONFIG.server.healthCheckInterval));
        }
        
        logError('服务器启动超时');
        return false;
    }

    // 启动测试服务器
    async startTestServer() {
        logInfo('启动测试服务器...');
        
        // 检查服务器是否已经运行
        if (await this.checkServerHealth()) {
            logInfo('服务器已在运行');
            return true;
        }
        
        // 启动服务器
        return new Promise((resolve, reject) => {
            this.serverProcess = spawn('npm', ['start'], {
                stdio: 'pipe',
                env: {
                    ...process.env,
                    NODE_ENV: 'test',
                    PORT: '50091'
                }
            });
            
            this.serverProcess.stdout.on('data', (data) => {
                const output = data.toString();
                if (output.includes('音乐解锁服务启动成功')) {
                    logSuccess('测试服务器启动成功');
                    resolve(true);
                }
            });
            
            this.serverProcess.stderr.on('data', (data) => {
                logError(`服务器错误: ${data.toString()}`);
            });
            
            this.serverProcess.on('error', (error) => {
                logError(`启动服务器失败: ${error.message}`);
                reject(error);
            });
            
            this.serverProcess.on('exit', (code) => {
                if (code !== 0) {
                    logError(`服务器异常退出，代码: ${code}`);
                    reject(new Error(`Server exited with code ${code}`));
                }
            });
            
            // 设置启动超时
            setTimeout(() => {
                if (this.serverProcess && !this.serverProcess.killed) {
                    logError('服务器启动超时');
                    this.stopTestServer();
                    reject(new Error('Server startup timeout'));
                }
            }, E2E_CONFIG.server.startupTimeout);
        });
    }

    // 停止测试服务器
    stopTestServer() {
        if (this.serverProcess && !this.serverProcess.killed) {
            logInfo('停止测试服务器...');
            this.serverProcess.kill('SIGTERM');
            
            // 强制杀死进程
            setTimeout(() => {
                if (this.serverProcess && !this.serverProcess.killed) {
                    this.serverProcess.kill('SIGKILL');
                }
            }, 5000);
            
            this.serverProcess = null;
            logSuccess('测试服务器已停止');
        }
    }

    // 运行Playwright测试
    async runPlaywrightTests() {
        logHeader('运行Playwright E2E测试');
        
        return new Promise((resolve, reject) => {
            const playwrightArgs = [
                'test',
                '--timeout=' + E2E_CONFIG.playwright.timeout,
                '--retries=' + E2E_CONFIG.playwright.retries
            ];
            
            if (E2E_CONFIG.playwright.headless) {
                playwrightArgs.push('--headed=false');
            }
            
            // 添加测试文件
            playwrightArgs.push(...E2E_CONFIG.testSuites);
            
            logInfo(`执行命令: npx playwright ${playwrightArgs.join(' ')}`);
            
            const playwright = spawn('npx', ['playwright', ...playwrightArgs], {
                stdio: 'inherit',
                shell: true
            });
            
            playwright.on('close', (code) => {
                const result = {
                    type: 'playwright',
                    success: code === 0,
                    exitCode: code,
                    timestamp: new Date().toISOString()
                };
                
                this.testResults.push(result);
                
                if (code === 0) {
                    logSuccess('Playwright E2E测试完成');
                    resolve(result);
                } else {
                    logError(`Playwright E2E测试失败，退出码: ${code}`);
                    resolve(result); // 不reject，继续执行
                }
            });
            
            playwright.on('error', (error) => {
                logError(`Playwright执行错误: ${error.message}`);
                const result = {
                    type: 'playwright',
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
                this.testResults.push(result);
                resolve(result);
            });
        });
    }

    // 运行API端点测试
    async runApiEndpointTests() {
        logHeader('运行API端点E2E测试');
        
        const endpoints = [
            { path: '/health', name: '健康检查' },
            { path: '/', name: 'API文档首页' },
            { path: '/music/source', name: '音源管理' }
        ];
        
        const results = [];
        
        for (const endpoint of endpoints) {
            logInfo(`测试端点: ${endpoint.name} (${endpoint.path})`);
            
            try {
                const response = await axios.get(`${E2E_CONFIG.server.baseUrl}${endpoint.path}`, {
                    timeout: 10000
                });
                
                const result = {
                    endpoint: endpoint.name,
                    path: endpoint.path,
                    success: response.status >= 200 && response.status < 400,
                    statusCode: response.status,
                    responseTime: response.headers['x-response-time'] || 'N/A'
                };
                
                results.push(result);
                
                if (result.success) {
                    logSuccess(`${endpoint.name}: ${response.status}`);
                } else {
                    logError(`${endpoint.name}: ${response.status}`);
                }
                
            } catch (error) {
                const result = {
                    endpoint: endpoint.name,
                    path: endpoint.path,
                    success: false,
                    error: error.message
                };
                
                results.push(result);
                logError(`${endpoint.name}: ${error.message}`);
            }
        }
        
        const testResult = {
            type: 'api-endpoints',
            success: results.every(r => r.success),
            results: results,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(testResult);
        return testResult;
    }

    // 生成E2E测试报告
    generateReport() {
        logHeader('生成E2E测试报告');
        
        const timestamp = new Date().toISOString();
        const reportPath = `test-reports/e2e-test-report-${timestamp.replace(/[:.]/g, '-')}.md`;
        
        // 确保报告目录存在
        const reportDir = path.dirname(reportPath);
        if (!fs.existsSync(reportDir)) {
            fs.mkdirSync(reportDir, { recursive: true });
        }
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - passedTests;
        const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(2) : 0;
        
        let report = `# 🌐 E2E测试报告

## 📊 测试执行摘要
- **执行时间**: ${new Date().toLocaleString()}
- **测试环境**: Node.js ${process.version}
- **服务器地址**: ${E2E_CONFIG.server.baseUrl}
- **浏览器**: ${E2E_CONFIG.playwright.browsers.join(', ')}

## 🎯 测试结果统计
- **总测试数**: ${totalTests}
- **通过测试**: ${passedTests}
- **失败测试**: ${failedTests}
- **成功率**: ${successRate}%

## 📈 详细测试结果

`;

        for (const result of this.testResults) {
            const status = result.success ? '✅ 通过' : '❌ 失败';
            
            report += `### ${result.type}
- **状态**: ${status}
- **执行时间**: ${result.timestamp}
`;
            
            if (result.error) {
                report += `- **错误**: ${result.error}\n`;
            }
            
            if (result.results) {
                report += `- **详细结果**:\n`;
                for (const subResult of result.results) {
                    const subStatus = subResult.success ? '✅' : '❌';
                    report += `  - ${subStatus} ${subResult.endpoint}: ${subResult.statusCode || subResult.error}\n`;
                }
            }
            
            report += '\n';
        }

        report += `## 📝 测试总结
${passedTests === totalTests ? 
    '✅ 所有E2E测试通过，系统端到端功能正常。' : 
    `⚠️ ${failedTests}个测试失败，请检查相关功能。`
}

## 🔧 环境配置
- **超时设置**: ${E2E_CONFIG.playwright.timeout}ms
- **重试次数**: ${E2E_CONFIG.playwright.retries}
- **无头模式**: ${E2E_CONFIG.playwright.headless}

---
*报告生成时间: ${timestamp}*
`;
        
        fs.writeFileSync(reportPath, report);
        logSuccess(`E2E测试报告已生成: ${reportPath}`);
        
        return reportPath;
    }
}

// 主函数
async function main() {
    const manager = new E2ETestManager();
    
    try {
        logHeader('UnblockNeteaseMusic Backend - E2E测试运行器');
        
        // 启动测试服务器
        await manager.startTestServer();
        
        // 等待服务器就绪
        const serverReady = await manager.waitForServer();
        if (!serverReady) {
            throw new Error('服务器启动失败');
        }
        
        // 运行API端点测试
        await manager.runApiEndpointTests();
        
        // 运行Playwright测试
        await manager.runPlaywrightTests();
        
        // 生成报告
        const reportPath = manager.generateReport();
        
        // 显示摘要
        logHeader('E2E测试完成');
        const totalTests = manager.testResults.length;
        const passedTests = manager.testResults.filter(r => r.success).length;
        
        logInfo(`测试报告: ${reportPath}`);
        logInfo(`总测试数: ${totalTests}`);
        logInfo(`通过测试: ${passedTests}`);
        logInfo(`失败测试: ${totalTests - passedTests}`);
        
        // 判断测试是否通过
        const allPassed = passedTests === totalTests;
        
        if (allPassed) {
            logSuccess('🎉 所有E2E测试通过！');
            process.exit(0);
        } else {
            logWarning('⚠️ 部分E2E测试失败，请查看报告');
            process.exit(1);
        }
        
    } catch (error) {
        logError(`E2E测试运行器错误: ${error.message}`);
        process.exit(1);
    } finally {
        // 清理资源
        manager.stopTestServer();
    }
}

// 错误处理
process.on('SIGINT', () => {
    console.log('\n收到中断信号，正在清理...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n收到终止信号，正在清理...');
    process.exit(0);
});

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { E2ETestManager };
