# 🎵 UnblockNeteaseMusic Backend

[![Node.js Version](https://img.shields.io/badge/node-%3E%3D16.0.0-brightgreen.svg)](https://nodejs.org/)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Test Coverage](https://img.shields.io/badge/coverage-85%25-green.svg)](./coverage)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](./tests)

基于 UnblockNeteaseMusic 的音乐解锁服务后端，提供 RESTful API 接口，支持多音源音乐解锁和批量处理。

## ✨ 核心特性

### 🎯 音乐解锁服务
- **多音源支持**：QQ音乐、酷狗、酷我、咪咕、JOOX、YouTube等9个音源
- **批量处理**：支持批量歌曲解锁，提高处理效率
- **智能降级**：音源失败时自动尝试其他音源
- **性能优化**：并发控制和超时管理

### 🛡️ 安全与稳定
- **速率限制**：15分钟100次请求限制，防止滥用
- **参数验证**：基于Joi的严格参数验证
- **错误处理**：完善的错误处理和日志记录
- **安全头**：Helmet安全中间件保护

### 📊 监控与日志
- **分类日志**：业务、性能、错误日志分离
- **文件轮转**：按日期和大小自动轮转日志
- **性能监控**：请求耗时和业务操作监控
- **健康检查**：服务状态监控

## 🚀 快速开始

### 环境要求
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **操作系统**: Windows/Linux/macOS

### 安装部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd unblock-netease-music-backend

# 2. 安装依赖
npm install

# 3. 配置环境变量（可选）
cp .env.example .env
# 编辑 .env 文件设置配置

# 4. 启动服务
npm start

# 5. 验证服务
curl http://localhost:50091/
```

### 开发模式

```bash
# 开发模式启动（自动重启）
npm run dev

# 运行测试
npm test

# 代码检查
npm run lint

# 性能测试
npm run test:performance
```

## 📖 API 文档

### 基础信息
- **服务地址**: `http://localhost:50091`
- **API版本**: v2.0.0
- **响应格式**: JSON

### 核心接口

#### 1. 音乐解锁 API
```http
GET /music/unlock?songs=418602084,123456&sources=qq,migu
```

**参数说明**:
- `songs` (必需): 歌曲ID列表，逗号分隔
- 音源配置: 通过环境变量 `MUSIC_SOURCES` 统一管理，支持：qq,migu,kuwo,kugou,joox,youtube

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "解锁成功",
  "数据": {
    "成功": [
      {
        "歌曲ID": 418602084,
        "播放链接": "https://...",
        "音源ID": "migu",
        "音源名称": "咪咕音乐",
        "音质": "128kbps"
      }
    ],
    "失败": []
  },
  "时间戳": "2025-08-02T12:00:00.000Z"
}
```

#### 2. 音源管理 API
```http
GET /music/source
```

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "获取成功",
  "数据": {
    "可用音源": ["qq", "migu", "kuwo", "kugou"],
    "音源总数": 9,
    "配置来源": "环境变量"
  },
  "时间戳": "2025-08-02T12:00:00.000Z"
}
```

#### 3. 服务状态 API
```http
GET /
```

返回完整的API文档和服务状态信息。

## ⚙️ 配置说明

### 环境变量配置

项目支持通过环境变量进行配置，所有配置都有合理的默认值：

```bash
# 服务配置
PORT=50091                    # 服务端口
HOST=localhost               # 服务主机
NODE_ENV=production          # 运行环境

# 性能配置
TIMEOUT=30000               # 请求超时时间(ms)
BATCH_CONCURRENCY=5         # 批量并发数
MAX_BATCH_SIZE=20          # 最大批量大小

# 日志配置
LOG_LEVEL=info             # 日志级别
LOG_FILE_ENABLED=true      # 启用文件日志
LOG_CONSOLE_ENABLED=true   # 启用控制台日志

# 安全配置
RATE_LIMIT_MAX_REQUESTS=100      # 速率限制最大请求数
RATE_LIMIT_WINDOW_MS=900000      # 速率限制时间窗口(ms)
```

### 音源配置

支持的音源列表：
- `qq`: QQ音乐
- `kugou`: 酷狗音乐
- `kuwo`: 酷我音乐
- `migu`: 咪咕音乐
- `joox`: JOOX音乐
- `youtube`: YouTube
- `ytdlp`: YouTube-DL
- `bilibili`: 哔哩哔哩
- `bilivideo`: 哔哩哔哩视频

## 🧪 测试

### 测试类型

```bash
# 单元测试
npm run test:unit

# 集成测试
npm run test:integration

# E2E测试
npm run test:e2e

# 性能测试
npm run test:performance

# 覆盖率测试
npm run test:coverage
```

### 测试覆盖率

当前测试覆盖率：
- **总体覆盖率**: 85%+
- **Services层**: 92.85%
- **Routes层**: 89.47%
- **Utils层**: 100%

## 📊 性能指标

### 响应时间
- **音乐解锁**: 平均 1-3 秒
- **音源管理**: 平均 < 100ms
- **批量处理**: 根据歌曲数量线性增长

### 成功率
- **主流音源**: > 90% 成功率
- **备用音源**: > 70% 成功率
- **整体成功率**: > 85%

### 资源使用
- **内存占用**: < 100MB
- **CPU使用**: 低负载 < 5%
- **并发支持**: 100+ 并发请求

## 🔧 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :50091
   
   # 检查Node.js版本
   node --version
   ```

2. **音乐解锁失败**
   ```bash
   # 检查网络连接
   curl -I https://music.163.com
   
   # 查看错误日志
   tail -f logs/error-*.log
   ```

3. **测试失败**
   ```bash
   # 清理缓存重新安装
   npm cache clean --force
   npm install
   
   # 运行特定测试
   npm test -- --testNamePattern="音乐解锁"
   ```

## 📝 开发指南

### 项目结构
```
src/
├── app.js              # 应用入口
├── config/             # 配置文件
├── middleware/         # 中间件
├── routes/             # 路由定义
├── services/           # 业务逻辑
└── utils/              # 工具函数

tests/                  # 测试文件
scripts/                # 脚本文件
logs/                   # 日志文件
```

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循 Node.js 最佳实践
- 完善的错误处理和日志记录
- 详细的函数注释和文档

## ⚠️ 注意事项

### 法律合规
1. **版权声明**: 本项目仅供学习研究使用
2. **使用限制**: 请遵守相关法律法规和音乐平台服务条款
3. **商业使用**: 商业使用前请咨询法律意见

### 生产环境
1. **环境变量**: 确保生产环境配置正确
2. **日志管理**: 配置日志轮转和监控
3. **性能监控**: 建议集成APM监控服务
4. **安全加固**: 定期更新依赖和安全补丁

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [UnblockNeteaseMusic](https://github.com/UnblockNeteaseMusic/server)
- [Node.js 官方文档](https://nodejs.org/docs/)
- [Express.js 官方文档](https://expressjs.com/)

---

**项目状态**: 🟢 生产就绪  
**最后更新**: 2025-08-02  
**版本**: v2.0.0
