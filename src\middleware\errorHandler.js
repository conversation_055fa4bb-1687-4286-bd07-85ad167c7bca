/**
 * 错误处理中间件模块
 * 统一处理应用中的各种错误和异常情况
 */

const { logError } = require('./logger');
const { HTTP_STATUS, ERROR_CODES } = require('../utils/constants');

/**
 * 智能错误堆栈过滤器
 * 区分有价值的业务错误堆栈和框架内部错误堆栈
 * @param {Error} error - 错误对象
 * @returns {boolean} - 是否应该显示堆栈信息
 */
function shouldShowStack(error) {
    // 生产环境永远不显示堆栈
    if (process.env.NODE_ENV === 'production') {
        return false;
    }

    // 开发环境下的智能过滤
    const stack = error.stack || '';

    // 框架内部错误特征（这些堆栈信息价值有限）
    const frameworkPatterns = [
        /at Layer\.handle/,                    // Express路由层
        /at trim_prefix/,                      // Express路由前缀处理
        /at router\.process_params/,           // Express参数处理
        /at router\.handle/,                   // Express路由处理
        /at router \(/,                        // Express路由器
        /node_modules[/\\]express[/\\]/,       // Express模块路径
        /notFoundHandler.*errorHandler\.js/    // 404处理器本身
    ];

    // 业务错误特征（这些堆栈信息有调试价值）
    const businessPatterns = [
        /src[/\\]routes[/\\]/,                 // 业务路由
        /src[/\\]services[/\\]/,               // 业务服务
        /src[/\\]controllers[/\\]/,            // 业务控制器
        /src[/\\]utils[/\\]/,                  // 业务工具
        /ValidationError/,                     // 参数验证错误
        /BusinessError/,                       // 业务逻辑错误
        /DatabaseError/,                       // 数据库错误
        /NetworkError/                         // 网络错误
    ];

    // 检查是否为业务错误
    const isBusinessError = businessPatterns.some(pattern => pattern.test(stack));
    if (isBusinessError) {
        return true;
    }

    // 检查是否为框架错误
    const isFrameworkError = frameworkPatterns.some(pattern => pattern.test(stack));
    if (isFrameworkError) {
        return false;
    }

    // 默认显示（未知类型的错误）
    return true;
}

/**
 * 创建智能错误日志记录函数
 * @param {Error} error - 错误对象
 * @param {Object} context - 上下文信息
 */
function logErrorSmart(error, context = {}) {
    const showStack = shouldShowStack(error);

    if (showStack) {
        // 显示完整堆栈（业务错误或未知错误）
        logError(error, context);
    } else {
        // 只显示错误消息，不显示堆栈（框架错误）
        const { logger } = require('./logger');
        logger.error('应用错误', {
            message: error.message,
            type: error.constructor.name,
            ...context
        });
    }
}

/**
 * 自定义错误类
 */
class AppError extends Error {
    constructor(message, statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR, errorCode = ERROR_CODES.INTERNAL_ERROR) {
        super(message);
        this.statusCode = statusCode;
        this.errorCode = errorCode;
        this.isOperational = true;

        Error.captureStackTrace(this, this.constructor);
    }
}

/**
 * 业务错误类
 */
class BusinessError extends AppError {
    constructor(message, errorCode) {
        super(message, HTTP_STATUS.BAD_REQUEST, errorCode);
    }
}

/**
 * 资源未找到错误类
 */
class NotFoundError extends AppError {
    constructor(message = '资源未找到') {
        super(message, HTTP_STATUS.NOT_FOUND, ERROR_CODES.SONG_NOT_FOUND);
    }
}

/**
 * 验证错误类
 */
class ValidationError extends AppError {
    constructor(message, details = null) {
        super(message, HTTP_STATUS.BAD_REQUEST, ERROR_CODES.VALIDATION_ERROR);
        this.details = details;
    }
}

/**
 * 服务不可用错误类
 */
class ServiceUnavailableError extends AppError {
    constructor(message = '服务暂时不可用') {
        super(message, HTTP_STATUS.SERVICE_UNAVAILABLE, ERROR_CODES.SOURCE_UNAVAILABLE);
    }
}

/**
 * 处理UnblockNeteaseMusic相关错误
 * @param {Error} error - 原始错误
 * @returns {AppError} 转换后的应用错误
 */
function handleUnblockError(error) {
    const message = error.message || '音乐解锁服务出错';
  
    // 根据错误消息判断错误类型
    if (message.includes('timeout') || message.includes('TIMEOUT')) {
        return new ServiceUnavailableError('音乐服务响应超时');
    }
  
    if (message.includes('not found') || message.includes('NOT_FOUND')) {
        return new NotFoundError('歌曲未找到');
    }
  
    if (message.includes('network') || message.includes('NETWORK')) {
        return new ServiceUnavailableError('网络连接错误');
    }
  
    return new AppError(message, HTTP_STATUS.INTERNAL_SERVER_ERROR, ERROR_CODES.UNLOCK_FAILED);
}

/**
 * 处理数据库错误
 * @param {Error} error - 数据库错误
 * @returns {AppError} 转换后的应用错误
 */
// function handleDatabaseError(error) {
//     logError(error, { type: 'database_error' });
//     return new AppError('数据库操作失败', HTTP_STATUS.INTERNAL_SERVER_ERROR, ERROR_CODES.INTERNAL_ERROR);
// }

/**
 * 处理网络请求错误
 * @param {Error} error - 网络错误
 * @returns {AppError} 转换后的应用错误
 */
function handleNetworkError(error) {
    if (error.code === 'ECONNREFUSED') {
        return new ServiceUnavailableError('无法连接到音乐服务');
    }
  
    if (error.code === 'ETIMEDOUT') {
        return new ServiceUnavailableError('请求超时');
    }
  
    return new ServiceUnavailableError('网络请求失败');
}

/**
 * 错误处理中间件
 * @param {Error} err - 错误对象
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function errorHandler(err, req, res, next) {
    // 如果响应已经发送，则交给默认的Express错误处理器
    if (res.headersSent) {
        return next(err);
    }

    let error = err;

    // 如果不是自定义错误，进行转换
    if (!(error instanceof AppError)) {
        // 处理不同类型的错误
        if (error.name === 'ValidationError') {
            error = new ValidationError(error.message, error.details);
        } else if (error.name === 'CastError') {
            error = new ValidationError('无效的参数格式');
        } else if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
            error = new ValidationError('JSON格式错误');
        } else if (error.name === 'TypeError' && error.message.includes('Cannot read')) {
            error = new ValidationError('参数缺失或格式错误');
        } else if (error.code && error.code.startsWith('E')) {
            error = handleNetworkError(error);
        } else if (error.message && error.message.includes('unblock')) {
            error = handleUnblockError(error);
        } else if (error.name === 'TimeoutError') {
            error = new ServiceUnavailableError('请求超时，请稍后重试');
        } else {
            error = new AppError(error.message || '服务器内部错误');
        }
    }

    // 记录错误日志（使用智能堆栈过滤）
    logErrorSmart(error, {
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.id,
        originalError: err.name
    });

    // 发送错误响应 - 使用中文字段名保持一致性
    // 安全策略：任何环境下都不在API响应中包含堆栈信息
    res.status(error.statusCode).json({
        状态码: error.statusCode,
        消息: error.message,
        时间戳: new Date().toISOString(),
        数据: null,
        错误代码: error.errorCode,
        ...(error.details && { 详情: error.details })
    });
}

/**
 * 404错误处理中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function notFoundHandler(req, res) {
    const path = req.url.toLowerCase();

    // 🎯 特殊处理：favicon.ico请求
    // 浏览器会自动请求favicon，对于纯API服务这是正常行为，不应记录为错误
    if (path === '/favicon.ico') {
        // 返回204 No Content，告诉浏览器没有favicon但这是正常的
        return res.status(204).end();
    }

    // 🎯 特殊处理：其他常见的浏览器自动请求
    if (path === '/robots.txt' || path === '/sitemap.xml' || path === '/apple-touch-icon.png') {
        // 对于这些文件，也返回204而不记录错误
        return res.status(204).end();
    }

    // 提供更友好的错误信息和建议
    let suggestion = '';

    if (path.includes('/api/')) {
        suggestion = '提示：请使用 /music/* 路径访问音乐服务API';
    } else if (path.includes('/health')) {
        suggestion = '提示：请访问根路径 / 获取服务状态';
    } else if (path.includes('/music/')) {
        suggestion = '提示：请检查路径是否正确，支持的端点：/music/unlock, /music/source';
    } else {
        suggestion = '提示：请访问根路径 / 查看API文档';
    }

    // 直接返回JSON格式的404错误响应，保持格式一致性
    const errorMessage = `路由 ${req.method} ${req.url} 未找到。${suggestion}`;

    // 记录错误日志（使用智能堆栈过滤）
    logErrorSmart(new NotFoundError(errorMessage), {
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.id
    });

    // 返回统一格式的JSON错误响应
    res.status(404).json({
        状态码: 404,
        消息: errorMessage,
        时间戳: new Date().toISOString(),
        数据: null,
        错误代码: 'NOT_FOUND'
    });
}

/**
 * 异步错误包装器
 * 用于包装异步路由处理函数，自动捕获Promise拒绝
 * @param {Function} fn - 异步函数
 * @returns {Function} 包装后的函数
 */
function asyncHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}

/**
 * 进程级错误处理
 */
function setupProcessErrorHandlers() {
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
        logError(error, { type: 'uncaught_exception' });
        process.exit(1);
    });

    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
        logError(new Error(reason), {
            type: 'unhandled_rejection',
            promise: promise.toString()
        });
    });
}

module.exports = {
    AppError,
    BusinessError,
    NotFoundError,
    ValidationError,
    ServiceUnavailableError,
    errorHandler,
    notFoundHandler,
    asyncHandler,
    setupProcessErrorHandlers
};
