#!/bin/bash

# ===================================================================
# UnblockNeteaseMusic Backend - 综合测试套件
# Comprehensive Test Suite for Music Unlock Service
# ===================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 测试配置
TEST_CONFIG_FILE="test-config.json"
REPORT_DIR="test-reports"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
REPORT_FILE="$REPORT_DIR/comprehensive-test-report-$TIMESTAMP.md"

# 创建测试配置
create_test_config() {
    cat > "$TEST_CONFIG_FILE" << EOF
{
  "server": {
    "host": "localhost",
    "port": 50091,
    "baseUrl": "http://localhost:50091"
  },
  "timeouts": {
    "unit": 30000,
    "integration": 60000,
    "e2e": 120000,
    "performance": 180000
  },
  "thresholds": {
    "coverage": 90,
    "performance": {
      "responseTime": 3000,
      "successRate": 95,
      "memoryLimit": 200
    }
  },
  "testData": {
    "songId": "418602084",
    "searchKeyword": "周杰伦",
    "batchSongs": ["418602084", "347230", "186016"]
  }
}
EOF
}

# 日志函数
log_header() {
    echo ""
    echo -e "${CYAN}=============================================="
    echo -e "🎯 $1"
    echo -e "==============================================${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# 环境检查
check_environment() {
    log_header "环境检查"
    
    # 检查Node.js版本
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_success "Node.js版本: $NODE_VERSION"
    else
        log_error "Node.js未安装"
        exit 1
    fi
    
    # 检查npm版本
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        log_success "npm版本: $NPM_VERSION"
    else
        log_error "npm未安装"
        exit 1
    fi
    
    # 检查项目文件
    REQUIRED_FILES=("package.json" "src/app.js" ".env")
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$file" ]; then
            log_success "项目文件存在: $file"
        else
            log_error "项目文件缺失: $file"
            exit 1
        fi
    done
    
    # 检查依赖
    log_info "检查项目依赖..."
    if npm ls --depth=0 &> /dev/null; then
        log_success "项目依赖完整"
    else
        log_warning "依赖可能存在问题，尝试安装..."
        npm install
    fi
    
    # 创建报告目录
    mkdir -p "$REPORT_DIR"
    log_success "测试环境检查完成"
}

# 代码质量检查
run_code_quality_check() {
    log_header "代码质量检查"
    
    log_test "运行ESLint代码检查..."
    if npm run lint; then
        log_success "ESLint检查通过"
        return 0
    else
        log_error "ESLint检查失败"
        return 1
    fi
}

# 单元测试
run_unit_tests() {
    log_header "单元测试"
    
    log_test "运行单元测试套件..."
    if npm run test:unit; then
        log_success "单元测试全部通过"
        return 0
    else
        log_error "单元测试失败"
        return 1
    fi
}

# 代码覆盖率测试
run_coverage_tests() {
    log_header "代码覆盖率测试"
    
    log_test "运行覆盖率测试..."
    if npm run test:coverage; then
        log_success "覆盖率测试完成"
        
        # 分析覆盖率报告
        if [ -f "coverage/lcov-report/index.html" ]; then
            log_info "覆盖率报告已生成: coverage/lcov-report/index.html"
        fi
        
        return 0
    else
        log_error "覆盖率测试失败"
        return 1
    fi
}

# 集成测试
run_integration_tests() {
    log_header "集成测试"
    
    log_test "运行集成测试..."
    if npm run test:integration; then
        log_success "集成测试通过"
        return 0
    else
        log_warning "集成测试失败 (可能需要外部服务)"
        return 0  # 不阻断流程
    fi
}

# 性能测试
run_performance_tests() {
    log_header "性能测试"
    
    log_test "运行性能测试..."
    if npm run test:performance; then
        log_success "性能测试完成"
        return 0
    else
        log_warning "性能测试失败 (可能需要服务器运行)"
        return 0  # 不阻断流程
    fi
}

# E2E测试
run_e2e_tests() {
    log_header "E2E测试"
    
    # 检查服务器是否运行
    if curl -s "http://localhost:50091/health" > /dev/null 2>&1; then
        log_test "运行E2E测试..."
        if npm run test:e2e; then
            log_success "E2E测试通过"
            return 0
        else
            log_error "E2E测试失败"
            return 1
        fi
    else
        log_warning "服务器未运行，跳过E2E测试"
        log_info "启动服务器: npm start"
        log_info "然后运行: npm run test:e2e"
        return 0
    fi
}

# API功能测试
run_api_tests() {
    log_header "API功能测试"
    
    # 检查服务器是否运行
    if curl -s "http://localhost:50091/health" > /dev/null 2>&1; then
        log_test "运行API功能测试..."
        
        # 测试健康检查
        log_test "测试健康检查API..."
        if curl -s "http://localhost:50091/health" | grep -q "healthy"; then
            log_success "健康检查API正常"
        else
            log_error "健康检查API异常"
        fi
        
        # 测试音源管理API
        log_test "测试音源管理API..."
        if curl -s "http://localhost:50091/music/source" | grep -q "音源配置"; then
            log_success "音源管理API正常"
        else
            log_error "音源管理API异常"
        fi
        
        return 0
    else
        log_warning "服务器未运行，跳过API测试"
        return 0
    fi
}

# 安全测试
run_security_tests() {
    log_header "安全测试"
    
    log_test "检查安全配置..."
    
    # 检查环境变量安全
    if grep -q "COOKIE" .env; then
        log_warning "发现Cookie配置，请确保生产环境安全"
    fi
    
    # 检查依赖安全
    log_test "运行依赖安全检查..."
    if npm audit --audit-level=high; then
        log_success "依赖安全检查通过"
    else
        log_warning "发现安全漏洞，建议运行 npm audit fix"
    fi
    
    return 0
}

# 生成综合测试报告
generate_comprehensive_report() {
    log_header "生成综合测试报告"
    
    cat > "$REPORT_FILE" << EOF
# 🧪 UnblockNeteaseMusic Backend - 综合测试报告

## 📊 测试执行摘要
- **执行时间**: $(date)
- **测试环境**: Node.js $(node --version)
- **项目版本**: v1.0.0
- **测试套件**: 综合测试套件 v1.0

## 🎯 测试范围
- ✅ 环境检查
- ✅ 代码质量检查 (ESLint)
- ✅ 单元测试 (Jest)
- ✅ 代码覆盖率测试
- ✅ 集成测试
- ✅ 性能测试
- ✅ E2E测试 (Playwright)
- ✅ API功能测试
- ✅ 安全测试

## 📈 测试结果统计
- **总测试数**: 8个测试套件
- **通过测试**: 待统计
- **失败测试**: 待统计
- **跳过测试**: 待统计

## 🔍 详细测试结果

### 代码质量
- **ESLint检查**: 待更新
- **代码规范**: 待更新

### 功能测试
- **单元测试**: 待更新
- **集成测试**: 待更新
- **API测试**: 待更新

### 性能测试
- **响应时间**: 待更新
- **并发处理**: 待更新
- **内存使用**: 待更新

### 安全测试
- **依赖安全**: 待更新
- **配置安全**: 待更新

## 📋 测试覆盖率
- **语句覆盖率**: 待更新
- **分支覆盖率**: 待更新
- **函数覆盖率**: 待更新
- **行覆盖率**: 待更新

## 🎯 质量指标
- **代码质量**: 待评估
- **功能完整性**: 待评估
- **性能表现**: 待评估
- **安全等级**: 待评估

## 📝 建议和改进
- 待分析测试结果后提供建议

## 📞 联系信息
- **测试执行**: 自动化测试套件
- **报告生成**: $(date)
- **版本**: v1.0.0

---
*本报告由综合测试套件自动生成*
EOF

    log_success "综合测试报告已生成: $REPORT_FILE"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    rm -f "$TEST_CONFIG_FILE"
    log_success "清理完成"
}

# 主函数
main() {
    log_header "UnblockNeteaseMusic Backend - 综合测试套件"
    
    # 记录开始时间
    START_TIME=$(date +%s)
    
    # 设置错误处理
    trap cleanup EXIT
    
    # 创建测试配置
    create_test_config
    
    # 执行测试流程
    check_environment
    
    # 统计测试结果
    TOTAL_TESTS=0
    PASSED_TESTS=0
    FAILED_TESTS=0
    
    # 运行各项测试
    tests=(
        "run_code_quality_check"
        "run_unit_tests"
        "run_coverage_tests"
        "run_integration_tests"
        "run_performance_tests"
        "run_e2e_tests"
        "run_api_tests"
        "run_security_tests"
    )
    
    for test in "${tests[@]}"; do
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        if $test; then
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    done
    
    # 生成报告
    generate_comprehensive_report
    
    # 计算执行时间
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    # 显示测试摘要
    log_header "测试执行完成"
    echo -e "${GREEN}📊 测试摘要:${NC}"
    echo -e "  总测试数: $TOTAL_TESTS"
    echo -e "  通过测试: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "  失败测试: ${RED}$FAILED_TESTS${NC}"
    echo -e "  执行时间: ${DURATION}秒"
    echo ""
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试通过！项目质量达标${NC}"
        exit 0
    else
        echo -e "${YELLOW}⚠️ 部分测试失败，请检查测试报告${NC}"
        exit 1
    fi
}

# 参数处理
case "${1:-all}" in
    "env")
        check_environment
        ;;
    "quality")
        check_environment
        run_code_quality_check
        ;;
    "unit")
        check_environment
        run_unit_tests
        ;;
    "coverage")
        check_environment
        run_coverage_tests
        ;;
    "integration")
        check_environment
        run_integration_tests
        ;;
    "performance")
        check_environment
        run_performance_tests
        ;;
    "e2e")
        check_environment
        run_e2e_tests
        ;;
    "api")
        check_environment
        run_api_tests
        ;;
    "security")
        check_environment
        run_security_tests
        ;;
    "all"|*)
        main
        ;;
esac
