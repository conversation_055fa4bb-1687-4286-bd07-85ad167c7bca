#!/usr/bin/env node

/**
 * UnblockNeteaseMusic Backend - 依赖管理器
 * Dependency Manager for Music Unlock Service
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 依赖管理配置
const DEPENDENCY_CONFIG = {
    production: {
        '@unblockneteasemusic/server': '^0.27.10',
        'express': '^4.18.2',
        'winston': '^3.11.0',
        'winston-daily-rotate-file': '^4.7.1',
        'dotenv': '^16.3.1',
        'joi': '^17.11.0',
        'helmet': '^7.1.0',
        'express-rate-limit': '^7.1.5'
    },
    development: {
        'jest': '^29.7.0',
        'supertest': '^6.3.4',
        '@playwright/test': '^1.54.1',
        'eslint': '^8.55.0',
        'nodemon': '^3.0.2',
        'axios': '^1.11.0'
    },
    security: {
        vulnerabilityThreshold: 'moderate',
        auditLevel: 'moderate',
        autoFix: true
    }
};

// 颜色定义
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// 日志函数
function log(level, message) {
    const timestamp = new Date().toISOString();
    const color = colors[level] || colors.reset;
    console.log(`${color}[${level.toUpperCase()}]${colors.reset} ${timestamp} - ${message}`);
}

function logHeader(title) {
    console.log('\n' + colors.cyan + '='.repeat(60));
    console.log(`📦 ${title}`);
    console.log('='.repeat(60) + colors.reset);
}

function logSuccess(message) {
    log('success', `✅ ${message}`);
}

function logError(message) {
    log('error', `❌ ${message}`);
}

function logInfo(message) {
    log('info', `ℹ️ ${message}`);
}

function logWarning(message) {
    log('warning', `⚠️ ${message}`);
}

// 依赖管理器
class DependencyManager {
    constructor() {
        this.packageJson = null;
        this.analysisResults = {
            outdated: [],
            vulnerabilities: [],
            unused: [],
            missing: []
        };
    }

    // 加载package.json
    loadPackageJson() {
        try {
            if (!fs.existsSync('package.json')) {
                throw new Error('package.json文件不存在');
            }
            
            this.packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            logSuccess('package.json加载成功');
            return true;
        } catch (error) {
            logError(`package.json加载失败: ${error.message}`);
            return false;
        }
    }

    // 分析依赖状态
    async analyzeDependencies() {
        logHeader('依赖分析');
        
        if (!this.loadPackageJson()) {
            return false;
        }
        
        // 检查过时的依赖
        await this.checkOutdatedDependencies();
        
        // 检查安全漏洞
        await this.checkVulnerabilities();
        
        // 检查缺失的依赖
        this.checkMissingDependencies();
        
        // 生成分析报告
        this.generateAnalysisReport();
        
        return true;
    }

    // 检查过时的依赖
    async checkOutdatedDependencies() {
        logInfo('检查过时的依赖...');
        
        try {
            const outdatedOutput = await this.runCommand('npm outdated --json');
            const outdatedData = JSON.parse(outdatedOutput || '{}');
            
            this.analysisResults.outdated = Object.entries(outdatedData).map(([name, info]) => ({
                name,
                current: info.current,
                wanted: info.wanted,
                latest: info.latest,
                type: info.type
            }));
            
            if (this.analysisResults.outdated.length > 0) {
                logWarning(`发现 ${this.analysisResults.outdated.length} 个过时的依赖`);
                for (const dep of this.analysisResults.outdated) {
                    logInfo(`  ${dep.name}: ${dep.current} → ${dep.latest}`);
                }
            } else {
                logSuccess('所有依赖都是最新版本');
            }
            
        } catch (error) {
            logWarning('无法检查过时的依赖');
        }
    }

    // 检查安全漏洞
    async checkVulnerabilities() {
        logInfo('检查安全漏洞...');
        
        try {
            // 尝试使用官方npm源进行安全检查
            await this.runCommand('npm config set registry https://registry.npmjs.org/');
            
            const auditOutput = await this.runCommand('npm audit --json');
            const auditData = JSON.parse(auditOutput || '{}');
            
            if (auditData.vulnerabilities) {
                const vulns = Object.entries(auditData.vulnerabilities).map(([name, info]) => ({
                    name,
                    severity: info.severity,
                    via: info.via,
                    effects: info.effects
                }));
                
                this.analysisResults.vulnerabilities = vulns;
                
                if (vulns.length > 0) {
                    logWarning(`发现 ${vulns.length} 个安全漏洞`);
                    for (const vuln of vulns) {
                        logWarning(`  ${vuln.name}: ${vuln.severity} 级别`);
                    }
                } else {
                    logSuccess('未发现安全漏洞');
                }
            }
            
        } catch (error) {
            logWarning('安全漏洞检查失败，可能是网络问题');
        }
    }

    // 检查缺失的依赖
    checkMissingDependencies() {
        logInfo('检查缺失的依赖...');
        
        const currentDeps = {
            ...this.packageJson.dependencies || {},
            ...this.packageJson.devDependencies || {}
        };
        
        const requiredDeps = {
            ...DEPENDENCY_CONFIG.production,
            ...DEPENDENCY_CONFIG.development
        };
        
        this.analysisResults.missing = Object.keys(requiredDeps).filter(dep => !currentDeps[dep]);
        
        if (this.analysisResults.missing.length > 0) {
            logWarning(`发现 ${this.analysisResults.missing.length} 个缺失的依赖`);
            for (const dep of this.analysisResults.missing) {
                logInfo(`  缺失: ${dep}`);
            }
        } else {
            logSuccess('所有必需依赖都已安装');
        }
    }

    // 优化依赖
    async optimizeDependencies() {
        logHeader('依赖优化');
        
        let optimized = false;
        
        // 安装缺失的依赖
        if (this.analysisResults.missing.length > 0) {
            logInfo('安装缺失的依赖...');
            for (const dep of this.analysisResults.missing) {
                try {
                    const version = DEPENDENCY_CONFIG.production[dep] || DEPENDENCY_CONFIG.development[dep];
                    const installCmd = DEPENDENCY_CONFIG.development[dep] ? 
                        `npm install --save-dev ${dep}@${version}` : 
                        `npm install --save ${dep}@${version}`;
                    
                    await this.runCommand(installCmd);
                    logSuccess(`已安装: ${dep}@${version}`);
                    optimized = true;
                } catch (error) {
                    logError(`安装失败: ${dep} - ${error.message}`);
                }
            }
        }
        
        // 修复安全漏洞
        if (this.analysisResults.vulnerabilities.length > 0 && DEPENDENCY_CONFIG.security.autoFix) {
            logInfo('修复安全漏洞...');
            try {
                await this.runCommand('npm audit fix');
                logSuccess('安全漏洞修复完成');
                optimized = true;
            } catch (error) {
                logWarning('自动修复安全漏洞失败');
            }
        }
        
        // 清理和重新安装
        if (optimized) {
            logInfo('清理和重新安装依赖...');
            try {
                await this.runCommand('npm cache clean --force');
                await this.runCommand('npm install');
                logSuccess('依赖重新安装完成');
            } catch (error) {
                logError('依赖重新安装失败');
            }
        }
        
        return optimized;
    }

    // 生成分析报告
    generateAnalysisReport() {
        const timestamp = new Date().toISOString();
        const reportPath = `dependency-analysis-report-${timestamp.replace(/[:.]/g, '-')}.md`;
        
        const report = `# 📦 依赖分析报告

## 📊 分析摘要
- **分析时间**: ${new Date().toLocaleString()}
- **Node.js版本**: ${process.version}
- **npm版本**: ${this.getNpmVersion()}
- **项目版本**: ${this.packageJson.version}

## 🎯 依赖统计
- **生产依赖**: ${Object.keys(this.packageJson.dependencies || {}).length}
- **开发依赖**: ${Object.keys(this.packageJson.devDependencies || {}).length}
- **过时依赖**: ${this.analysisResults.outdated.length}
- **安全漏洞**: ${this.analysisResults.vulnerabilities.length}
- **缺失依赖**: ${this.analysisResults.missing.length}

## 📈 详细分析

### 过时的依赖
${this.analysisResults.outdated.length > 0 ? 
    this.analysisResults.outdated.map(dep => 
        `- **${dep.name}**: ${dep.current} → ${dep.latest} (${dep.type})`
    ).join('\n') : 
    '✅ 所有依赖都是最新版本'
}

### 安全漏洞
${this.analysisResults.vulnerabilities.length > 0 ? 
    this.analysisResults.vulnerabilities.map(vuln => 
        `- **${vuln.name}**: ${vuln.severity} 级别`
    ).join('\n') : 
    '✅ 未发现安全漏洞'
}

### 缺失的依赖
${this.analysisResults.missing.length > 0 ? 
    this.analysisResults.missing.map(dep => `- ${dep}`).join('\n') : 
    '✅ 所有必需依赖都已安装'
}

## 📝 优化建议
${this.generateOptimizationSuggestions()}

## 🔧 维护建议
- 定期运行 \`npm run deps:check\` 检查依赖状态
- 及时更新有安全漏洞的依赖
- 保持依赖版本的一致性
- 定期清理不使用的依赖

---
*报告生成时间: ${timestamp}*
`;
        
        fs.writeFileSync(reportPath, report);
        logSuccess(`依赖分析报告已生成: ${reportPath}`);
        
        return reportPath;
    }

    // 生成优化建议
    generateOptimizationSuggestions() {
        const suggestions = [];
        
        if (this.analysisResults.outdated.length > 0) {
            suggestions.push('- 更新过时的依赖: `npm update`');
        }
        
        if (this.analysisResults.vulnerabilities.length > 0) {
            suggestions.push('- 修复安全漏洞: `npm audit fix`');
        }
        
        if (this.analysisResults.missing.length > 0) {
            suggestions.push('- 安装缺失的依赖: `npm run deps:install`');
        }
        
        if (suggestions.length === 0) {
            return '✅ 依赖状态良好，无需优化。';
        }
        
        return suggestions.join('\n');
    }

    // 获取npm版本
    getNpmVersion() {
        try {
            return require('child_process').execSync('npm --version', { encoding: 'utf8' }).trim();
        } catch (error) {
            return 'unknown';
        }
    }

    // 运行命令
    runCommand(command, timeout = 60000) {
        return new Promise((resolve, reject) => {
            const child = spawn(command, {
                shell: true,
                stdio: 'pipe'
            });
            
            let stdout = '';
            let stderr = '';
            
            child.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            
            child.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            
            const timer = setTimeout(() => {
                child.kill();
                reject(new Error('Command timeout'));
            }, timeout);
            
            child.on('close', (code) => {
                clearTimeout(timer);
                if (code === 0) {
                    resolve(stdout.trim());
                } else {
                    reject(new Error(stderr || `Command failed with code ${code}`));
                }
            });
            
            child.on('error', (error) => {
                clearTimeout(timer);
                reject(error);
            });
        });
    }
}

// 主函数
async function main() {
    try {
        logHeader('UnblockNeteaseMusic Backend - 依赖管理器');
        
        const manager = new DependencyManager();
        
        // 解析命令行参数
        const args = process.argv.slice(2);
        const command = args[0] || 'analyze';
        
        switch (command) {
            case 'analyze':
                await manager.analyzeDependencies();
                break;
            case 'optimize':
                await manager.analyzeDependencies();
                await manager.optimizeDependencies();
                break;
            case 'install':
                await manager.analyzeDependencies();
                if (manager.analysisResults.missing.length > 0) {
                    await manager.optimizeDependencies();
                } else {
                    logInfo('没有缺失的依赖需要安装');
                }
                break;
            default:
                logError(`未知命令: ${command}`);
                logInfo('可用命令: analyze, optimize, install');
                process.exit(1);
        }
        
        logSuccess('依赖管理完成');
        
    } catch (error) {
        logError(`依赖管理器错误: ${error.message}`);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { DependencyManager };
