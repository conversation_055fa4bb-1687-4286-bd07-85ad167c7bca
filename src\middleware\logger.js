/**
 * 日志中间件模块
 * 基于Winston实现的日志记录系统，支持文件和控制台输出
 */

const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const config = require('../config/config');

// 生产环境简洁日志格式
const productionFormat = winston.format.combine(
    winston.format.timestamp({
        format: config.logging.timeFormat
    }),
    winston.format.printf(({ level, message, timestamp }) => {
        return `${timestamp} [${level.toUpperCase()}]: ${message}`;
    })
);

// 开发环境详细日志格式
const developmentFormat = winston.format.combine(
    winston.format.timestamp({
        format: config.logging.timeFormat
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, stack, ...meta }) => {
        let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;

        // 如果有额外的元数据，添加到日志中
        if (Object.keys(meta).length > 0) {
            log += ` | ${JSON.stringify(meta)}`;
        }

        // 如果有错误堆栈，添加到日志中（仅开发环境）
        if (stack && process.env.NODE_ENV !== 'production') {
            log += `\n${stack}`;
        }

        return log;
    })
);



// 创建传输器数组
const transports = [];

// 控制台输出配置
if (config.logging.consoleEnabled) {
    // 生产环境使用简洁格式，开发环境使用彩色格式
    const consoleFormat = process.env.NODE_ENV === 'production'
        ? productionFormat
        : winston.format.combine(
            winston.format.colorize(),
            developmentFormat
        );

    transports.push(
        new winston.transports.Console({
            format: consoleFormat
        })
    );
}

// 文件输出配置
if (config.logging.fileEnabled) {
    // 错误日志文件
    transports.push(
        new DailyRotateFile({
            filename: path.join('logs', 'error-%DATE%.log'),
            datePattern: config.logging.datePattern,
            level: 'error',
            maxFiles: config.logging.maxFiles,
            maxSize: config.logging.maxSize,
            format: developmentFormat  // 文件日志始终保持详细格式
        })
    );

    // 组合日志文件
    transports.push(
        new DailyRotateFile({
            filename: path.join('logs', 'combined-%DATE%.log'),
            datePattern: config.logging.datePattern,
            maxFiles: config.logging.maxFiles,
            maxSize: config.logging.maxSize,
            format: developmentFormat  // 文件日志始终保持详细格式
        })
    );

    // 访问日志文件 (P2硬编码优化)
    transports.push(
        new DailyRotateFile({
            filename: path.join('logs', 'access-%DATE%.log'),
            datePattern: config.logging.datePattern,
            level: 'info',
            maxFiles: config.logging.maxFiles,
            maxSize: config.logging.maxSize,
            format: developmentFormat  // 文件日志始终保持详细格式
        })
    );
}

// 创建logger实例
const logger = winston.createLogger({
    level: config.logging.level,
    format: developmentFormat,  // 默认使用详细格式，传输器会覆盖
    transports,
    // 处理未捕获的异常
    exceptionHandlers: config.logging.fileEnabled ? [
        new DailyRotateFile({
            filename: path.join('logs', 'exceptions-%DATE%.log'),
            datePattern: config.logging.datePattern,
            maxFiles: config.logging.maxFiles,
            maxSize: config.logging.maxSize
        })
    ] : [],
    // 处理未处理的Promise拒绝
    rejectionHandlers: config.logging.fileEnabled ? [
        new DailyRotateFile({
            filename: path.join('logs', 'rejections-%DATE%.log'),
            datePattern: config.logging.datePattern,
            maxFiles: config.logging.maxFiles,
            maxSize: config.logging.maxSize
        })
    ] : []
});

/**
 * Express请求日志中间件
 * 记录每个HTTP请求的详细信息
 */
function requestLogger(req, res, next) {
    const startTime = Date.now();
    const requestId = req.id || generateRequestId();

    // 缓存常用信息，避免重复获取
    const requestInfo = {
        method: req.method,
        url: req.url,
        ip: req.ip || req.connection?.remoteAddress || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        requestId
    };

    // 只在debug模式下记录请求开始
    if (config.logging.level === 'debug') {
        logger.info('请求开始', requestInfo);
    }

    // 监听响应结束事件
    res.on('finish', () => {
        const duration = Date.now() - startTime;
        const logLevel = res.statusCode >= 400 ? 'warn' : 'info';

        // 只记录重要信息，减少内存使用
        const responseInfo = {
            ...requestInfo,
            statusCode: res.statusCode,
            duration: `${duration}ms`
        };

        // 移除不必要的字段以节省内存
        if (responseInfo.userAgent === 'unknown') {
            delete responseInfo.userAgent;
        }

        logger[logLevel]('请求完成', responseInfo);
    });

    next();
}

/**
 * 生成请求ID
 * @returns {string} 唯一的请求ID
 */
function generateRequestId() {
    return Math.random().toString(36).substring(2, 11);
}

/**
 * 错误日志记录函数
 * 安全策略：任何环境下都不记录堆栈信息到日志
 * @param {Error} error - 错误对象
 * @param {Object} context - 上下文信息
 */
function logError(error, context = {}) {
    logger.error('应用错误', {
        message: error.message,
        type: error.constructor.name,
        ...context
    });
}

/**
 * 性能日志记录函数
 * @param {string} operation - 操作名称
 * @param {number} duration - 执行时间(毫秒)
 * @param {Object} metadata - 元数据
 */
function logPerformance(operation, duration, metadata = {}) {
    logger.info('性能监控', {
        operation,
        duration: `${duration}ms`,
        ...metadata
    });
}

/**
 * 业务日志记录函数
 * @param {string} action - 业务动作
 * @param {Object} data - 业务数据
 */
function logBusiness(action, data = {}) {
    logger.info('业务操作', {
        action,
        ...data
    });
}

module.exports = {
    logger,
    requestLogger,
    logError,
    logPerformance,
    logBusiness
};
