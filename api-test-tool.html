<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UnblockNeteaseMusic API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .card {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 { color: #333; text-align: center; }
        h3 { color: #666; margin-bottom: 15px; }
        input, button {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input { width: 100%; }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover { background: #0056b3; }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .api-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
        .api-item:last-child { border-bottom: none; }
        .url {
            background: #e9ecef;
            padding: 5px;
            border-radius: 3px;
            font-family: monospace;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>UnblockNeteaseMusic API 测试</h1>

    <div class="card">
        <h3>服务器设置</h3>
        <input type="text" id="server" value="http://localhost:50090" placeholder="服务器地址">
        <button onclick="openUrl(document.getElementById('server').value)">打开服务器</button>
    </div>

    <div class="card">
        <h3>常用API</h3>
        <div class="api-item">
            <strong>健康检查</strong>
            <div class="url">GET /</div>
            <button onclick="openApi('/')">测试</button>
        </div>
        <div class="api-item">
            <strong>音乐解锁</strong>
            <div class="url">GET /music/unlock?songs=歌曲ID</div>
            <input type="text" id="songs" placeholder="歌曲ID，多个用逗号分隔" value="418602084">
            <button onclick="testUnlock()">测试解锁</button>
        </div>
        <div class="api-item">
            <strong>自定义测试</strong>
            <input type="text" id="custom" placeholder="输入API路径，如 /status">
            <button onclick="testCustom()">测试</button>
        </div>
    </div>

    <div class="card">
        <h3>说明</h3>
        <p>• 由于CORS限制，点击测试按钮会在新窗口打开API</p>
        <p>• 可以直接在浏览器地址栏查看API响应</p>
        <p>• 确保UnblockNeteaseMusic服务已启动</p>
    </div>

    <script>
        function getServer() {
            return document.getElementById('server').value || 'http://localhost:50090';
        }

        function openUrl(url) {
            window.open(url, '_blank');
        }

        function openApi(path) {
            const url = getServer() + path;
            window.open(url, '_blank');
        }

        function testUnlock() {
            const songs = document.getElementById('songs').value;
            if (!songs) {
                alert('请输入歌曲ID');
                return;
            }
            const url = getServer() + '/music/unlock?songs=' + encodeURIComponent(songs);
            window.open(url, '_blank');
        }

        function testCustom() {
            const path = document.getElementById('custom').value;
            if (!path) {
                alert('请输入API路径');
                return;
            }
            const url = getServer() + (path.startsWith('/') ? path : '/' + path);
            window.open(url, '_blank');
        }
    </script>
</body>
</html>
