#!/usr/bin/env node

/**
 * 硬编码优化测试脚本
 * 用于验证P0、P1、P2三阶段硬编码优化的完整性和正确性
 * 生成时间: 2025-08-01 19:53:02
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const levelColors = {
        INFO: colors.blue,
        SUCCESS: colors.green,
        WARNING: colors.yellow,
        ERROR: colors.red,
        TEST: colors.magenta
    };
    
    const color = levelColors[level] || colors.reset;
    console.log(`${color}[${level}]${colors.reset} ${message}`);
    
    if (data) {
        console.log(JSON.stringify(data, null, 2));
    }
}

// 测试配置
const TEST_CONFIG = {
    serverUrl: 'http://localhost:50091',
    timeout: 10000,
    testCases: {
        p0: [
            'timeout.unlock',
            'timeout.sourceTest', 
            'timeout.apiRequest',
            'timeout.healthCheck',
            'performance.batchConcurrency',
            'performance.maxRetries',
            'performance.retryDelay',
            'limits.maxBatchSize',
            'limits.maxSearchResults',
            'limits.maxKeywordLength',
            'limits.requestTimeout',
            'cache.metadataTTL',
            'cache.searchTTL',
            'cache.unlockTTL',
            'cache.enabled'
        ],
        p1: [
            'constants.DEFAULT_TIMEOUT',
            'constants.MAX_RETRIES',
            'constants.RETRY_DELAY',
            'constants.BATCH_SIZE',
            'constants.SEARCH_LIMIT',
            'constants.KEYWORD_MAX_LENGTH',
            'constants.CACHE_TTL',
            'constants.MIN_BITRATE'
        ],
        p2: [
            'logging.datePattern',
            'logging.timeFormat',
            'testing.defaultSongId',
            'testing.defaultSongIds',
            'testing.testKeywords',
            'testing.testSources',
            'testing.testArtists',
            'testing.testAlbums',
            'ui.theme.primaryColor',
            'ui.theme.secondaryColor',
            'ui.theme.accentColor',
            'ui.theme.successColor',
            'ui.theme.errorColor',
            'ui.theme.warningColor',
            'ui.layout.maxWidth',
            'ui.layout.headerFontSize',
            'ui.layout.baseFontSize'
        ]
    }
};

// 获取配置值的辅助函数
function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
}

// 测试配置文件加载
async function testConfigLoading() {
    log('TEST', '测试配置文件加载...');
    
    try {
        // 测试config.js加载
        const configPath = path.join(process.cwd(), 'src/config/config.js');
        if (!fs.existsSync(configPath)) {
            throw new Error('配置文件不存在: src/config/config.js');
        }
        
        delete require.cache[require.resolve(configPath)];
        const config = require(configPath);
        
        if (!config || typeof config !== 'object') {
            throw new Error('配置文件未导出有效的配置对象');
        }
        
        log('SUCCESS', '配置文件加载成功');
        return config;
    } catch (error) {
        log('ERROR', `配置文件加载失败: ${error.message}`);
        return null;
    }
}

// 测试环境变量
async function testEnvironmentVariables() {
    log('TEST', '测试环境变量配置...');
    
    const requiredEnvVars = [
        // P0阶段环境变量
        'UNLOCK_TIMEOUT', 'SOURCE_TEST_TIMEOUT', 'API_REQUEST_TIMEOUT', 'HEALTH_CHECK_TIMEOUT',
        'BATCH_CONCURRENCY', 'MAX_RETRIES', 'RETRY_DELAY',
        // P1阶段环境变量  
        'DEFAULT_TIMEOUT', 'MAX_RETRIES_CONST', 'RETRY_DELAY_CONST', 'BATCH_SIZE',
        'SEARCH_LIMIT', 'KEYWORD_MAX_LENGTH', 'CACHE_TTL', 'MIN_BITRATE',
        // P2阶段环境变量
        'LOG_DATE_PATTERN', 'LOG_TIME_FORMAT', 'TEST_SONG_ID', 'TEST_SONG_IDS',
        'TEST_KEYWORDS', 'TEST_SOURCES', 'TEST_ARTISTS', 'TEST_ALBUMS',
        'UI_PRIMARY_COLOR', 'UI_SECONDARY_COLOR', 'UI_ACCENT_COLOR', 'UI_SUCCESS_COLOR',
        'UI_ERROR_COLOR', 'UI_WARNING_COLOR', 'UI_MAX_WIDTH', 'UI_HEADER_FONT_SIZE', 'UI_BASE_FONT_SIZE'
    ];
    
    const envPath = path.join(process.cwd(), '.env');
    if (!fs.existsSync(envPath)) {
        log('ERROR', '.env文件不存在');
        return false;
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
            const [key, ...valueParts] = trimmed.split('=');
            if (key && valueParts.length > 0) {
                envVars[key.trim()] = valueParts.join('=').trim();
            }
        }
    });
    
    let missingVars = 0;
    for (const varName of requiredEnvVars) {
        if (!envVars[varName]) {
            log('WARNING', `环境变量 ${varName} 未设置`);
            missingVars++;
        }
    }
    
    if (missingVars === 0) {
        log('SUCCESS', `所有${requiredEnvVars.length}个环境变量已正确设置`);
        return true;
    } else {
        log('WARNING', `${missingVars}个环境变量缺失，将使用默认值`);
        return true; // 允许使用默认值
    }
}

// 测试配置完整性
async function testConfigCompleteness(config) {
    log('TEST', '测试配置完整性...');
    
    let totalTests = 0;
    let passedTests = 0;
    
    // 测试P0阶段配置
    for (const configPath of TEST_CONFIG.testCases.p0) {
        totalTests++;
        const value = getNestedValue(config, configPath);
        if (value !== undefined) {
            passedTests++;
            log('SUCCESS', `P0配置 ${configPath}: ${value}`);
        } else {
            log('ERROR', `P0配置 ${configPath} 缺失`);
        }
    }
    
    // 测试P1阶段配置
    for (const configPath of TEST_CONFIG.testCases.p1) {
        totalTests++;
        const value = getNestedValue(config, configPath);
        if (value !== undefined) {
            passedTests++;
            log('SUCCESS', `P1配置 ${configPath}: ${value}`);
        } else {
            log('ERROR', `P1配置 ${configPath} 缺失`);
        }
    }
    
    // 测试P2阶段配置
    for (const configPath of TEST_CONFIG.testCases.p2) {
        totalTests++;
        const value = getNestedValue(config, configPath);
        if (value !== undefined) {
            passedTests++;
            log('SUCCESS', `P2配置 ${configPath}: ${value}`);
        } else {
            log('ERROR', `P2配置 ${configPath} 缺失`);
        }
    }
    
    const completeness = (passedTests / totalTests * 100).toFixed(1);
    log('INFO', `配置完整性: ${passedTests}/${totalTests} (${completeness}%)`);
    
    return passedTests === totalTests;
}

// 测试服务器连接
async function testServerConnection() {
    log('TEST', '测试服务器连接...');
    
    return new Promise((resolve) => {
        const req = http.get(`${TEST_CONFIG.serverUrl}/api/health`, { timeout: TEST_CONFIG.timeout }, (res) => {
            if (res.statusCode === 200) {
                log('SUCCESS', '服务器连接成功');
                resolve(true);
            } else {
                log('WARNING', `服务器响应状态码: ${res.statusCode}`);
                resolve(false);
            }
        });
        
        req.on('error', (error) => {
            log('WARNING', `服务器连接失败: ${error.message}`);
            resolve(false);
        });
        
        req.on('timeout', () => {
            log('WARNING', '服务器连接超时');
            req.destroy();
            resolve(false);
        });
    });
}

// 测试配置API
async function testConfigAPI() {
    log('TEST', '测试配置API...');
    
    return new Promise((resolve) => {
        const req = http.get(`${TEST_CONFIG.serverUrl}/api/config`, { timeout: TEST_CONFIG.timeout }, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const configData = JSON.parse(data);
                    
                    if (configData.testing && configData.ui) {
                        log('SUCCESS', '配置API响应正确');
                        log('INFO', `测试配置: ${Object.keys(configData.testing).length}项`);
                        log('INFO', `UI配置: ${Object.keys(configData.ui).length}项`);
                        resolve(true);
                    } else {
                        log('ERROR', '配置API响应格式错误');
                        resolve(false);
                    }
                } catch (error) {
                    log('ERROR', `配置API响应解析失败: ${error.message}`);
                    resolve(false);
                }
            });
        });
        
        req.on('error', (error) => {
            log('ERROR', `配置API请求失败: ${error.message}`);
            resolve(false);
        });
        
        req.on('timeout', () => {
            log('ERROR', '配置API请求超时');
            req.destroy();
            resolve(false);
        });
    });
}

// 生成测试报告
function generateTestReport(results) {
    const timestamp = new Date().toISOString();
    const reportPath = `test-hardcode-optimization-report-${timestamp.replace(/[:.]/g, '-')}.md`;
    
    let report = `# 硬编码优化测试报告\n\n`;
    report += `**生成时间**: ${timestamp}\n\n`;
    
    // 测试结果汇总
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(result => result).length;
    const successRate = (passedTests / totalTests * 100).toFixed(1);
    
    if (passedTests === totalTests) {
        report += `## ✅ 测试状态: 全部通过 (${successRate}%)\n\n`;
    } else {
        report += `## ⚠️ 测试状态: 部分通过 (${successRate}%)\n\n`;
    }
    
    // 详细测试结果
    report += `## 📊 测试结果详情\n\n`;
    
    Object.entries(results).forEach(([testName, passed]) => {
        const status = passed ? '✅' : '❌';
        report += `- ${status} ${testName}\n`;
    });
    
    report += `\n## 📈 测试统计\n\n`;
    report += `- **总测试数**: ${totalTests}\n`;
    report += `- **通过测试**: ${passedTests}\n`;
    report += `- **失败测试**: ${totalTests - passedTests}\n`;
    report += `- **成功率**: ${successRate}%\n\n`;
    
    if (passedTests === totalTests) {
        report += `## 🎉 结论\n\n硬编码优化测试全部通过，系统配置化改造成功！\n`;
    } else {
        report += `## ⚠️ 建议\n\n部分测试未通过，建议检查相关配置和服务状态。\n`;
    }
    
    fs.writeFileSync(reportPath, report);
    log('SUCCESS', `测试报告已生成: ${reportPath}`);
}

// 主测试函数
async function runTests() {
    console.log(`${colors.cyan}=== 硬编码优化测试工具 ===${colors.reset}`);
    console.log('验证P0、P1、P2三阶段硬编码优化的完整性\n');
    
    const results = {};
    
    // 1. 测试配置文件加载
    const config = await testConfigLoading();
    results['配置文件加载'] = config !== null;
    
    if (!config) {
        log('ERROR', '配置文件加载失败，终止测试');
        return;
    }
    
    // 2. 测试环境变量
    results['环境变量配置'] = await testEnvironmentVariables();
    
    // 3. 测试配置完整性
    results['配置完整性'] = await testConfigCompleteness(config);
    
    // 4. 测试服务器连接
    results['服务器连接'] = await testServerConnection();
    
    // 5. 测试配置API
    if (results['服务器连接']) {
        results['配置API'] = await testConfigAPI();
    } else {
        results['配置API'] = false;
        log('WARNING', '跳过配置API测试（服务器未连接）');
    }
    
    // 生成测试报告
    generateTestReport(results);
    
    // 输出最终结果
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(result => result).length;
    
    console.log(`\n${colors.cyan}=== 测试完成 ===${colors.reset}`);
    
    if (passedTests === totalTests) {
        log('SUCCESS', `所有测试通过！(${passedTests}/${totalTests})`);
        process.exit(0);
    } else {
        log('WARNING', `部分测试失败 (${passedTests}/${totalTests})`);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    runTests().catch(error => {
        log('ERROR', `测试执行失败: ${error.message}`);
        process.exit(1);
    });
}

module.exports = {
    runTests,
    testConfigLoading,
    testEnvironmentVariables,
    testConfigCompleteness,
    testServerConnection,
    testConfigAPI
};
