/**
 * 简化的参数验证中间件
 */

const Joi = require('joi');
const { ValidationError } = require('./errorHandler');

/**
 * 创建简化验证中间件
 */
function createValidator(schema, source = 'query') {
    return (req, _res, next) => {
        const { error, value } = schema.validate(req[source], {
            convert: true,
            allowUnknown: false,
            stripUnknown: true
        });
        if (error) {
            return next(new ValidationError(error.details[0].message));
        }
        req[source] = value;
        next();
    };
}

// 简化的验证模式
const validators = {
    // 音乐解锁API验证 - 仅支持songs参数，强制使用环境变量配置的音源
    validateMusicUnlock: createValidator(Joi.object({
        songs: Joi.string().pattern(/^[\d,]+$/).required()
    })),

    // 获取音源列表验证
    validateGetSources: createValidator(Joi.object({
        includeStatus: Joi.boolean().default(false)
    }))
};

module.exports = { validators };
