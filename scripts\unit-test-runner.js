#!/usr/bin/env node

/**
 * UnblockNeteaseMusic Backend - 单元测试运行器
 * Unit Test Runner for Music Unlock Service
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// 测试配置
const TEST_CONFIG = {
    timeout: 30000,
    coverage: {
        threshold: 90,
        reporters: ['text', 'lcov', 'html']
    },
    testFiles: [
        'tests/api-unit.test.js',
        'tests/utils.test.js',
        'tests/services.test.js',
        'tests/module-init.test.js',
        'tests/coverage-boost.test.js'
    ]
};

// 日志函数
function log(level, message) {
    const timestamp = new Date().toISOString();
    const color = colors[level] || colors.reset;
    console.log(`${color}[${level.toUpperCase()}]${colors.reset} ${timestamp} - ${message}`);
}

function logHeader(title) {
    console.log('\n' + colors.cyan + '='.repeat(60));
    console.log(`🧪 ${title}`);
    console.log('='.repeat(60) + colors.reset);
}

function logSuccess(message) {
    log('success', `✅ ${message}`);
}

function logError(message) {
    log('error', `❌ ${message}`);
}

function logInfo(message) {
    log('info', `ℹ️ ${message}`);
}

function logWarning(message) {
    log('warning', `⚠️ ${message}`);
}

// 环境检查
function checkEnvironment() {
    logHeader('环境检查');
    
    // 检查Node.js版本
    const nodeVersion = process.version;
    logInfo(`Node.js版本: ${nodeVersion}`);
    
    // 检查项目文件
    const requiredFiles = ['package.json', 'src/app.js', 'jest.config.js'];
    for (const file of requiredFiles) {
        if (fs.existsSync(file)) {
            logSuccess(`项目文件存在: ${file}`);
        } else {
            logError(`项目文件缺失: ${file}`);
            process.exit(1);
        }
    }
    
    // 检查测试文件
    logInfo('检查测试文件...');
    let testFileCount = 0;
    for (const testFile of TEST_CONFIG.testFiles) {
        if (fs.existsSync(testFile)) {
            logSuccess(`测试文件存在: ${testFile}`);
            testFileCount++;
        } else {
            logWarning(`测试文件缺失: ${testFile}`);
        }
    }
    
    logInfo(`发现 ${testFileCount} 个测试文件`);
    
    // 检查Jest配置
    if (fs.existsSync('package.json')) {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        if (packageJson.jest) {
            logSuccess('Jest配置存在');
        } else {
            logWarning('Jest配置缺失，使用默认配置');
        }
    }
    
    logSuccess('环境检查完成');
}

// 运行Jest测试
function runJestTests(options = {}) {
    return new Promise((resolve, reject) => {
        logHeader('运行Jest单元测试');
        
        const jestArgs = [
            '--testTimeout=' + TEST_CONFIG.timeout,
            '--verbose'
        ];
        
        // 添加覆盖率选项
        if (options.coverage) {
            jestArgs.push('--coverage');
            jestArgs.push('--coverageReporters=' + TEST_CONFIG.coverage.reporters.join(','));
        }
        
        // 添加监听模式
        if (options.watch) {
            jestArgs.push('--watch');
        }
        
        // 添加测试文件
        if (options.testFiles) {
            jestArgs.push(...options.testFiles);
        } else {
            jestArgs.push(...TEST_CONFIG.testFiles);
        }
        
        logInfo(`执行命令: npx jest ${jestArgs.join(' ')}`);
        
        const jest = spawn('npx', ['jest', ...jestArgs], {
            stdio: 'inherit',
            shell: true
        });
        
        jest.on('close', (code) => {
            if (code === 0) {
                logSuccess('Jest测试完成');
                resolve({ success: true, code });
            } else {
                logError(`Jest测试失败，退出码: ${code}`);
                resolve({ success: false, code });
            }
        });
        
        jest.on('error', (error) => {
            logError(`Jest执行错误: ${error.message}`);
            reject(error);
        });
    });
}

// 分析覆盖率报告
function analyzeCoverageReport() {
    logHeader('分析覆盖率报告');
    
    const coverageDir = 'coverage';
    const lcovReportPath = path.join(coverageDir, 'lcov-report', 'index.html');
    const jsonReportPath = path.join(coverageDir, 'coverage-final.json');
    
    if (fs.existsSync(lcovReportPath)) {
        logSuccess(`HTML覆盖率报告: ${lcovReportPath}`);
    }
    
    if (fs.existsSync(jsonReportPath)) {
        try {
            const coverageData = JSON.parse(fs.readFileSync(jsonReportPath, 'utf8'));
            const files = Object.keys(coverageData);
            
            logInfo(`覆盖率分析 - 共 ${files.length} 个文件`);
            
            let totalStatements = 0;
            let coveredStatements = 0;
            
            for (const file of files) {
                const fileCoverage = coverageData[file];
                const statements = fileCoverage.s;
                const statementMap = fileCoverage.statementMap;
                
                const fileStatements = Object.keys(statementMap).length;
                const fileCovered = Object.values(statements).filter(count => count > 0).length;
                
                totalStatements += fileStatements;
                coveredStatements += fileCovered;
                
                const filePercentage = fileStatements > 0 ? (fileCovered / fileStatements * 100).toFixed(2) : 0;
                logInfo(`${path.basename(file)}: ${filePercentage}% (${fileCovered}/${fileStatements})`);
            }
            
            const overallPercentage = totalStatements > 0 ? (coveredStatements / totalStatements * 100).toFixed(2) : 0;
            
            if (overallPercentage >= TEST_CONFIG.coverage.threshold) {
                logSuccess(`总体覆盖率: ${overallPercentage}% (达到目标 ${TEST_CONFIG.coverage.threshold}%)`);
            } else {
                logWarning(`总体覆盖率: ${overallPercentage}% (未达到目标 ${TEST_CONFIG.coverage.threshold}%)`);
            }
            
        } catch (error) {
            logError(`解析覆盖率报告失败: ${error.message}`);
        }
    } else {
        logWarning('覆盖率JSON报告不存在');
    }
}

// 生成测试报告
function generateTestReport(results) {
    logHeader('生成测试报告');
    
    const timestamp = new Date().toISOString();
    const reportPath = `test-reports/unit-test-report-${timestamp.replace(/[:.]/g, '-')}.md`;
    
    // 确保报告目录存在
    const reportDir = path.dirname(reportPath);
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const report = `# 🧪 单元测试报告

## 📊 测试执行摘要
- **执行时间**: ${new Date().toLocaleString()}
- **测试环境**: Node.js ${process.version}
- **Jest版本**: 29.7.0
- **项目版本**: v1.0.0

## 🎯 测试结果
- **测试状态**: ${results.success ? '✅ 通过' : '❌ 失败'}
- **退出码**: ${results.code}
- **测试文件数**: ${TEST_CONFIG.testFiles.length}

## 📈 测试覆盖率
${results.coverage ? '✅ 已生成覆盖率报告' : '❌ 未生成覆盖率报告'}

## 📁 测试文件清单
${TEST_CONFIG.testFiles.map(file => `- ${file}`).join('\n')}

## 🔍 详细信息
- **超时设置**: ${TEST_CONFIG.timeout}ms
- **覆盖率目标**: ${TEST_CONFIG.coverage.threshold}%
- **报告格式**: ${TEST_CONFIG.coverage.reporters.join(', ')}

## 📝 建议
${results.success ? 
    '✅ 所有单元测试通过，代码质量良好。' : 
    '❌ 部分测试失败，请检查测试输出并修复问题。'
}

---
*报告生成时间: ${timestamp}*
`;
    
    fs.writeFileSync(reportPath, report);
    logSuccess(`测试报告已生成: ${reportPath}`);
    
    return reportPath;
}

// 主函数
async function main() {
    try {
        logHeader('UnblockNeteaseMusic Backend - 单元测试运行器');
        
        // 解析命令行参数
        const args = process.argv.slice(2);
        const options = {
            coverage: args.includes('--coverage'),
            watch: args.includes('--watch'),
            testFiles: args.filter(arg => !arg.startsWith('--'))
        };
        
        logInfo(`运行选项: ${JSON.stringify(options)}`);
        
        // 环境检查
        checkEnvironment();
        
        // 运行测试
        const results = await runJestTests(options);
        
        // 分析覆盖率（如果启用）
        if (options.coverage) {
            analyzeCoverageReport();
            results.coverage = true;
        }
        
        // 生成报告
        const reportPath = generateTestReport(results);
        
        // 显示摘要
        console.log('\n' + colors.cyan + '📊 测试摘要' + colors.reset);
        console.log(`测试状态: ${results.success ? colors.green + '✅ 通过' : colors.red + '❌ 失败'}${colors.reset}`);
        console.log(`测试报告: ${reportPath}`);
        
        if (options.coverage) {
            console.log(`覆盖率报告: coverage/lcov-report/index.html`);
        }
        
        // 退出
        process.exit(results.success ? 0 : 1);
        
    } catch (error) {
        logError(`单元测试运行器错误: ${error.message}`);
        process.exit(1);
    }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
    logError(`未处理的Promise拒绝: ${reason}`);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    logError(`未捕获的异常: ${error.message}`);
    process.exit(1);
});

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    runJestTests,
    analyzeCoverageReport,
    generateTestReport
};
