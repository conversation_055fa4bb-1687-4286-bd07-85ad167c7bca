/**
 * 应用常量定义模块
 * 定义系统中使用的所有常量和枚举值
 */

// HTTP状态码
const HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_ALLOWED: 405,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503
};



// 支持的音源列表
const MUSIC_SOURCES = {
    QQ: 'qq',
    KUGOU: 'kugou',
    KUWO: 'kuwo',
    MIGU: 'migu',
    JOOX: 'joox',
    YOUTUBE: 'youtube',
    YTDLP: 'ytdlp',
    BILIBILI: 'bilibili',
    BILIVIDEO: 'bilivideo'
};

// 音源显示名称映射
const SOURCE_DISPLAY_NAMES = {
    [MUSIC_SOURCES.QQ]: 'QQ音乐',
    [MUSIC_SOURCES.KUGOU]: '酷狗音乐',
    [MUSIC_SOURCES.KUWO]: '酷我音乐',
    [MUSIC_SOURCES.MIGU]: '咪咕音乐',
    [MUSIC_SOURCES.JOOX]: 'JOOX',
    [MUSIC_SOURCES.YOUTUBE]: 'YouTube',
    [MUSIC_SOURCES.YTDLP]: 'YouTube (yt-dlp)',
    [MUSIC_SOURCES.BILIBILI]: 'B站音乐',
    [MUSIC_SOURCES.BILIVIDEO]: 'B站视频'
};

// 音质等级
const QUALITY_LEVELS = {
    LOW: 128000,      // 128kbps
    STANDARD: 192000, // 192kbps
    HIGH: 320000,     // 320kbps
    LOSSLESS: 999000  // 无损
};

// 音质等级显示名称
const QUALITY_DISPLAY_NAMES = {
    [QUALITY_LEVELS.LOW]: '标准',
    [QUALITY_LEVELS.STANDARD]: '较高',
    [QUALITY_LEVELS.HIGH]: '极高',
    [QUALITY_LEVELS.LOSSLESS]: '无损'
};



// 导入配置文件
const config = require('../config/config');

// API限制
const API_LIMITS = {
    MAX_BATCH_SIZE: config.performance.maxBatchSize,        // 批量操作最大数量
    REQUEST_TIMEOUT: config.performance.timeout    // 请求超时时间(毫秒)
};

// 错误代码
const ERROR_CODES = {
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    SONG_NOT_FOUND: 'SONG_NOT_FOUND',
    SEARCH_FAILED: 'SEARCH_FAILED',
    UNLOCK_FAILED: 'UNLOCK_FAILED',
    SOURCE_UNAVAILABLE: 'SOURCE_UNAVAILABLE',
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
    INTERNAL_ERROR: 'INTERNAL_ERROR'
};

module.exports = {
    HTTP_STATUS,
    MUSIC_SOURCES,
    SOURCE_DISPLAY_NAMES,
    QUALITY_LEVELS,
    QUALITY_DISPLAY_NAMES,
    API_LIMITS,
    ERROR_CODES
};
