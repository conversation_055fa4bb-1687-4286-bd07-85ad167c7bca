#!/bin/bash

# =============================================================================
# UnblockNeteaseMusic Backend API 测试脚本
# 版本: v1.0.0
# 创建时间: 2025-08-02
# 描述: 自动化测试音乐解锁服务的各项功能
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
BASE_URL="http://localhost:50091"
TEST_SONG_ID="418602084"
TEST_SOURCES="qq,migu,kuwo"
TIMEOUT=30

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试函数
test_api() {
    local test_name="$1"
    local url="$2"
    local expected_status="$3"
    local method="${4:-GET}"
    local data="$5"
    
    ((TOTAL_TESTS++))
    log_info "测试 $TOTAL_TESTS: $test_name"
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "\n%{http_code}" -X POST -H "Content-Type: application/json" -d "$data" --max-time $TIMEOUT "$url")
    else
        response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT "$url")
    fi
    
    if [ $? -ne 0 ]; then
        log_error "请求失败: $test_name"
        return 1
    fi
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "$expected_status" ]; then
        log_success "$test_name - HTTP状态码: $http_code"
        echo "响应内容: $(echo "$response_body" | jq -r '.消息 // .message // "无消息"' 2>/dev/null || echo "解析失败")"
        return 0
    else
        log_error "$test_name - 期望状态码: $expected_status, 实际状态码: $http_code"
        echo "响应内容: $response_body"
        return 1
    fi
}

# 检查服务是否运行
check_service() {
    log_info "检查服务状态..."
    if curl -s --max-time 5 "$BASE_URL" > /dev/null; then
        log_success "服务运行正常"
        return 0
    else
        log_error "服务未运行，请先启动服务: npm start"
        exit 1
    fi
}

# 主要测试套件
run_tests() {
    echo "=============================================="
    echo "🎵 UnblockNeteaseMusic Backend API 测试"
    echo "=============================================="
    echo "测试服务器: $BASE_URL"
    echo "测试歌曲ID: $TEST_SONG_ID"
    echo "测试音源: $TEST_SOURCES"
    echo "=============================================="
    echo
    
    # 检查服务状态
    check_service
    echo
    
    # 1. 基础服务测试
    log_info "=== 基础服务测试 ==="
    test_api "服务根路径访问" "$BASE_URL/" "200"
    echo
    
    # 2. 音乐解锁功能测试
    log_info "=== 音乐解锁功能测试 ==="
    
    # 2.1 单首歌曲解锁 - 正常情况
    test_api "单首歌曲解锁" "$BASE_URL/music/unlock?songs=$TEST_SONG_ID" "200"

    # 2.2 批量歌曲解锁
    test_api "批量歌曲解锁" "$BASE_URL/music/unlock?songs=$TEST_SONG_ID,123456" "200"

    # 2.3 参数验证测试
    test_api "缺少songs参数" "$BASE_URL/music/unlock" "400"
    test_api "空的songs参数" "$BASE_URL/music/unlock?songs=" "400"
    test_api "无效的歌曲ID格式" "$BASE_URL/music/unlock?songs=invalid_id" "400"

    # 2.4 已废弃参数测试 (应该返回400错误)
    test_api "使用已废弃的sources参数" "$BASE_URL/music/unlock?sources=migu&songs=$TEST_SONG_ID" "400"
    
    echo
    
    # 3. 音源管理功能测试
    log_info "=== 音源管理功能测试 ==="
    
    # 3.1 获取音源信息
    test_api "获取音源管理信息" "$BASE_URL/music/source" "200"
    
    echo
    
    # 4. 错误处理测试
    log_info "=== 错误处理测试 ==="
    
    # 4.1 不存在的路由
    test_api "不存在的路由" "$BASE_URL/nonexistent" "404"
    test_api "不存在的音乐路由" "$BASE_URL/music/nonexistent" "404"
    
    # 4.2 方法不允许
    test_api "POST方法到unlock端点" "$BASE_URL/music/unlock" "404" "POST" '{"songs":["'$TEST_SONG_ID'"],"sources":["migu"]}'
    
    echo
    
    # 5. 性能测试
    log_info "=== 性能测试 ==="
    
    # 5.1 并发请求测试
    log_info "并发请求测试 (5个并发请求)"
    start_time=$(date +%s)
    
    for i in {1..5}; do
        curl -s "$BASE_URL/music/source" > /dev/null &
    done
    wait
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    if [ $duration -le 10 ]; then
        log_success "并发性能测试通过 - 耗时: ${duration}秒"
        ((PASSED_TESTS++))
    else
        log_error "并发性能测试失败 - 耗时过长: ${duration}秒"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
    
    echo
}

# 生成测试报告
generate_report() {
    echo "=============================================="
    echo "📊 测试报告"
    echo "=============================================="
    echo "总测试数: $TOTAL_TESTS"
    echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "测试结果: ${GREEN}全部通过 ✅${NC}"
        success_rate="100%"
    else
        success_rate=$(( PASSED_TESTS * 100 / TOTAL_TESTS ))
        echo -e "成功率: ${YELLOW}${success_rate}%${NC}"
        
        if [ $success_rate -ge 80 ]; then
            echo -e "测试结果: ${YELLOW}大部分通过 ⚠️${NC}"
        else
            echo -e "测试结果: ${RED}需要修复 ❌${NC}"
        fi
    fi
    
    echo "=============================================="
    echo "测试完成时间: $(date)"
    echo "=============================================="
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    # 这里可以添加清理逻辑，如果需要的话
}

# 主函数
main() {
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 命令未找到，请安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 命令未找到，JSON解析功能将受限"
    fi
    
    # 运行测试
    run_tests
    
    # 生成报告
    generate_report
    
    # 清理
    cleanup
    
    # 退出码
    if [ $FAILED_TESTS -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 脚本入口
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "UnblockNeteaseMusic Backend API 测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --url URL      指定服务器地址 (默认: http://localhost:50091)"
    echo "  --timeout SEC  设置请求超时时间 (默认: 30秒)"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置运行测试"
    echo "  $0 --url http://localhost:3000       # 指定服务器地址"
    echo "  $0 --timeout 60                      # 设置60秒超时"
    exit 0
fi

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --url)
            BASE_URL="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            echo "使用 $0 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 运行主函数
main
