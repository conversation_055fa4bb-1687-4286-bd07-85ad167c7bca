/**
 * 音乐服务统一路由
 * 包含音乐解锁和音源管理功能
 */

const express = require('express');
const router = express.Router();
const { unlockSong } = require('../services/unlockService');
const { asyncHandler } = require('../middleware/errorHandler');
const { logBusiness, logPerformance } = require('../middleware/logger');
const { validators } = require('../middleware/validator');
const { MUSIC_SOURCES, SOURCE_DISPLAY_NAMES } = require('../utils/constants');
const config = require('../config/config');

/**
 * 根据比特率获取音质描述
 */
function getBitrateDescription(bitrate) {
    if (bitrate >= 999000) return '无损';
    if (bitrate >= 320000) return '高品质';
    if (bitrate >= 192000) return '较高品质';
    if (bitrate >= 128000) return '标准';
    if (bitrate >= 96000) return '普通';
    return '低品质';
}

/**
 * 获取音源名称
 */
function getSourceName(sourceId) {
    const sourceNames = {
        qq: 'QQ音乐',
        kugou: '酷狗音乐',
        kuwo: '酷我音乐',
        migu: '咪咕音乐',
        joox: 'JOOX音乐',
        youtube: 'YouTube Music'
    };
    return sourceNames[sourceId] || sourceId;
}

/**
 * 格式化解锁结果
 */
function formatUnlockResult(result, sourceId, priority) {
    return {
        歌曲ID: result.歌曲ID,
        播放链接: result.播放链接,
        音源ID: sourceId,
        音源名称: getSourceName(sourceId),
        音质: result.音质 || 0,
        音质描述: getBitrateDescription(result.音质 || 0),
        文件大小: result.文件大小 || 0,
        格式: result.格式 || 'mp3',
        优先级: priority,
        解锁时间: new Date().toISOString()
    };
}

/**
 * 解锁单首歌曲
 */
async function unlockSingleSong(songId, sources) {
    const startTime = Date.now();

    // 按优先级顺序尝试每个音源
    for (let i = 0; i < sources.length; i++) {
        const source = sources[i];
        const priority = i + 1;

        try {
            logBusiness('尝试解锁歌曲', { songId, source, priority });

            const result = await unlockSong(songId, [source]);

            if (result && result.播放链接) {
                const duration = Date.now() - startTime;
                logPerformance('歌曲解锁成功', {
                    operation: '单首歌曲解锁',
                    duration: `${duration}ms`,
                    songId,
                    source,
                    success: true
                });

                return {
                    success: true,
                    data: formatUnlockResult(result, source, priority)
                };
            }
        } catch (error) {
            logBusiness('音源解锁失败', { songId, source, error: error.message });
            continue; // 尝试下一个音源
        }
    }

    // 所有音源都失败
    const duration = Date.now() - startTime;
    logPerformance('歌曲解锁失败', {
        operation: '单首歌曲解锁',
        duration: `${duration}ms`,
        songId,
        success: false
    });

    return {
        success: false,
        error: '所有指定音源均无法解锁该歌曲',
        songId
    };
}

/**
 * 获取音源配置
 * 统一使用环境变量配置，简化配置管理
 */
function getSourcesConfig() {
    // 系统默认音源（兜底配置）
    const systemDefaultSources = ['qq', 'migu', 'kuwo', 'kugou'];

    // 使用环境变量配置（优先）
    if (config.music.sources && config.music.sources.length > 0) {
        return {
            sources: config.music.sources,
            configSource: '环境变量',
            originalInput: process.env.MUSIC_SOURCES || ''
        };
    }

    // 使用系统默认配置（兜底）
    return {
        sources: systemDefaultSources,
        configSource: '系统默认',
        originalInput: systemDefaultSources.join(',')
    };
}

/**
 * 简化的音乐解锁API端点
 * GET /music/unlock?songs=418602084,123456 (使用环境变量配置的音源)
 */
router.get('/unlock', validators.validateMusicUnlock, asyncHandler(async (req, res) => {
    const startTime = Date.now();

    // 参数已通过validator中间件验证，直接解析使用
    const { songs } = req.query;

    // 获取音源配置（统一使用环境变量）
    const sourceConfig = getSourcesConfig();
    const sourcesArray = sourceConfig.sources;
    const songsArray = songs.split(',').map(s => s.trim()).filter(s => s);

    // 转换歌曲ID为数字（validator已确保格式正确）
    const songIds = songsArray.map(id => parseInt(id, 10));

    logBusiness('音乐解锁API请求', {
        sources: sourcesArray,
        songs: songIds,
        count: songIds.length,
        configSource: sourceConfig.configSource,
        originalInput: sourceConfig.originalInput
    });

    // 并行处理所有歌曲
    const results = await Promise.all(
        songIds.map(songId => unlockSingleSong(songId, sourcesArray))
    );

    // 分类结果
    const successResults = results.filter(r => r.success).map(r => r.data);
    const failureResults = results.filter(r => !r.success).map(r => ({
        歌曲ID: r.songId,
        失败原因: r.error,
        尝试音源: sourcesArray,
        失败时间: new Date().toISOString()
    }));

    // 计算统计信息
    const totalCount = songIds.length;
    const successCount = successResults.length;
    const failureCount = failureResults.length;
    const successRate = totalCount > 0 ? ((successCount / totalCount) * 100).toFixed(1) + '%' : '0%';

    // 构建响应 - 保持格式一致性：单首歌曲时返回对象，多首歌曲时返回数组
    const responseData = {
        状态码: 200,
        消息: totalCount === 1 ? '单首歌曲解锁完成' : '批量解锁完成',
        时间戳: new Date().toISOString(),
        音源配置来源: sourceConfig.configSource,
        使用的音源: sourcesArray,
        解锁总数: totalCount,
        解锁成功: successCount,
        解锁失败: failureCount,
        解锁成功率: successRate,
        成功列表: totalCount === 1 ? (successResults[0] || {}) : successResults,
        失败列表: totalCount === 1 ? (failureResults[0] || {}) : failureResults
    };

    // 记录性能
    const duration = Date.now() - startTime;
    logPerformance('音乐解锁API完成', {
        operation: '批量音乐解锁',
        duration: `${duration}ms`,
        totalCount,
        successCount,
        failureCount,
        successRate
    });

    res.json(responseData);
}));

/**
 * 音源管理API端点
 * GET /music/source
 */
router.get('/source', validators.validateGetSources, asyncHandler(async (_req, res) => {
    logBusiness('获取音源管理信息');

    const currentTime = new Date().toISOString();

    // 获取所有可用音源和用户启用的音源
    const allSourceIds = Object.values(MUSIC_SOURCES);
    const enabledSourceIds = config.music.sources;
    const disabledSourceIds = allSourceIds.filter(id => !enabledSourceIds.includes(id));

    // 构建音源配置信息
    const sourceConfig = enabledSourceIds.map((sourceId, index) => ({
        优先级: index + 1,
        音源ID: sourceId,
        音源名称: SOURCE_DISPLAY_NAMES[sourceId] || sourceId,
        需要Cookie: ['qq', 'joox'].includes(sourceId)
    }));

    // 构建已启用音源对象
    const enabledSourcesObj = {};
    enabledSourceIds.forEach(sourceId => {
        enabledSourcesObj[sourceId] = SOURCE_DISPLAY_NAMES[sourceId] || sourceId;
    });

    // 构建已禁用音源对象
    const disabledSourcesObj = {};
    disabledSourceIds.forEach(sourceId => {
        disabledSourcesObj[sourceId] = SOURCE_DISPLAY_NAMES[sourceId] || sourceId;
    });

    // 构建响应数据，采用平铺结构
    const responseData = {
        状态码: 200,
        消息: '音源管理服务',
        时间戳: currentTime,
        启用无损音质: config.music.enableFlac === 'true',
        启用本地VIP: config.music.enableLocalVip === 'true',
        严格按音源顺序: config.music.followSourceOrder === 'true',
        屏蔽广告: config.music.blockAds === 'true',
        选择最高音质: config.music.selectMaxBr === 'true',
        音源总数: allSourceIds.length
    };

    // 然后添加音源相关信息
    responseData.已启用音源 = enabledSourcesObj;
    responseData.已禁用音源 = disabledSourcesObj;
    responseData.音源配置 = sourceConfig;

    res.json(responseData);
}));



module.exports = router;
