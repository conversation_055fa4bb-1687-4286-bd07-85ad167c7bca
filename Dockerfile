# 🐳 音乐解锁服务 - Docker配置文件
# 多阶段构建，优化镜像大小和安全性

# 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖 (仅生产依赖)
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY src/ ./src/
COPY public/ ./public/

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S musicapp -u 1001

# 生产阶段
FROM node:18-alpine AS production

# 安装必要的系统包
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S musicapp -u 1001

# 从构建阶段复制文件
COPY --from=builder --chown=musicapp:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=musicapp:nodejs /app/src ./src
COPY --from=builder --chown=musicapp:nodejs /app/public ./public
COPY --chown=musicapp:nodejs package*.json ./

# 创建必要的目录
RUN mkdir -p logs && \
    chown -R musicapp:nodejs logs

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000
ENV LOG_LEVEL=info

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# 切换到非root用户
USER musicapp

# 使用dumb-init作为PID 1
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["node", "src/app.js"]

# 元数据标签
LABEL maintainer="开发团队" \
      version="1.0.0" \
      description="音乐解锁服务后端" \
      org.opencontainers.image.title="Music Unlock Server" \
      org.opencontainers.image.description="基于UnblockNeteaseMusic的音乐解锁服务" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="开发团队"
