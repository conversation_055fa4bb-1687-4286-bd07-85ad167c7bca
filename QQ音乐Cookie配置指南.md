# QQ音乐Cookie配置指南

## 问题描述
QQ音乐解锁失败，返回"This song \"?\" is not available in any source"错误，原因是缺少必要的Cookie认证。

## 解决方案

### 1. 获取QQ音乐Cookie

1. **打开浏览器**，访问 https://y.qq.com/
2. **登录QQ音乐账号**（建议使用有VIP权限的账号）
3. **打开开发者工具** (F12)
4. **切换到Network标签**
5. **刷新页面**，查找任意一个对 `y.qq.com` 的请求
6. **复制Cookie值**，格式类似：
   ```
   uin=123456789; skey=abcdefg; p_skey=hijklmn; ...
   ```

### 2. 配置Cookie到项目

#### 方法A：环境变量配置
在 `.env` 文件中添加：
```bash
# QQ音乐Cookie认证
QQ_COOKIE="uin=123456789; skey=abcdefg; p_skey=hijklmn; ..."
```

#### 方法B：代码配置
在 `src/services/unlockService.js` 中添加：
```javascript
// QQ音乐Cookie配置
if (config.music.qqCookie) {
    process.env.QQ_COOKIE = config.music.qqCookie;
}
```

### 3. 验证配置

运行测试命令验证QQ音乐是否可以正常解锁：
```bash
curl "http://localhost:50090/music/unlock?sources=qq&songs=418602084"
```

## 注意事项

1. **Cookie有效期**：QQ音乐Cookie通常有时效性，需要定期更新
2. **账号权限**：建议使用VIP账号获取更好的音质和更多歌曲
3. **安全性**：Cookie包含敏感信息，请妥善保管
4. **备选方案**：建议配置多个音源作为备选，提高解锁成功率

## 推荐配置

```bash
# 推荐的音源优先级配置
MUSIC_SOURCES=migu,kuwo,qq,kugou,joox,youtube

# 如果有QQ音乐Cookie，可以将qq放在前面
# MUSIC_SOURCES=qq,migu,kuwo,kugou,joox,youtube
```
